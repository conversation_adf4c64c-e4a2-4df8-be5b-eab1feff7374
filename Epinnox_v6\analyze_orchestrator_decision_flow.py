#!/usr/bin/env python3
"""
LLM Orchestrator Decision Flow Analysis
Traces through the exact decision-making process to identify inconsistencies
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm_response_parsers import LLMResponseParsers
from core.llm_orchestrator import PromptType, PromptResult
from datetime import datetime

def analyze_response_parsing():
    """Analyze response parsing for the specific log scenario"""
    print("🔍 ANALYZING RESPONSE PARSING")
    print("=" * 60)
    
    parser = LLMResponseParsers()
    
    # Simulate the exact log scenario responses
    test_responses = {
        'risk_assessment': "DECISION: SHORT\nCONFIDENCE: 85%\nEXPLANATION: Strong bearish momentum detected",
        'entry_timing': "DECISION: SHORT\nACTION: ENTER_NOW\nCONFIDENCE: 75%\nENTRY_TYPE: LIMIT",
        'opportunity_scanner': "DECISION: LONG\nCONFIDENCE: 80%\nBEST_OPPORTUNITY: BREAKOUT\nSETUP_TYPE: MOMENTUM"
    }
    
    parsed_results = {}
    
    for prompt_type, response_text in test_responses.items():
        print(f"\n📝 Testing {prompt_type.upper()}:")
        print(f"   Raw Response: {response_text}")
        
        if prompt_type == 'risk_assessment':
            parsed = parser.parse_risk_assessment_response(response_text)
        elif prompt_type == 'entry_timing':
            parsed = parser.parse_entry_timing_response(response_text)
        elif prompt_type == 'opportunity_scanner':
            parsed = parser.parse_opportunity_scanner_response(response_text)
        
        parsed_results[prompt_type] = parsed
        print(f"   Parsed Result: {parsed}")
        
        # Check if DECISION is preserved
        if 'DECISION' in parsed:
            print(f"   ✅ DECISION preserved: {parsed['DECISION']}")
        else:
            print(f"   ❌ DECISION lost in parsing")
        
        # Check confidence
        confidence = parsed.get('CONFIDENCE', 0)
        print(f"   📊 Confidence: {confidence}%")
        
        # Check specific fields
        if prompt_type == 'risk_assessment':
            approved = parsed.get('APPROVED', False)
            risk_score = parsed.get('RISK_SCORE', 100)
            print(f"   🎯 Risk Assessment: {'APPROVED' if approved else 'REJECTED'} (Score: {risk_score})")
            
            # This is the key issue - check if it shows "REJECTED" instead of preserving SHORT
            if parsed.get('DECISION') == 'SHORT' and approved:
                print(f"   ✅ Correctly parsed SHORT decision as APPROVED")
            elif not approved:
                print(f"   ❌ ISSUE: SHORT decision incorrectly marked as REJECTED")
        
        elif prompt_type == 'entry_timing':
            action = parsed.get('ACTION', 'WAIT')
            decision = parsed.get('DECISION', None)
            print(f"   🎯 Entry Timing: ACTION={action}, DECISION={decision}")
            
            if action == 'ENTER_NOW' and decision == 'SHORT':
                print(f"   ✅ Correctly parsed SHORT ENTER_NOW")
            else:
                print(f"   ❌ ISSUE: Expected SHORT ENTER_NOW, got {decision} {action}")
        
        elif prompt_type == 'opportunity_scanner':
            decision = parsed.get('DECISION', None)
            best_opp = parsed.get('BEST_OPPORTUNITY', 'NONE')
            print(f"   🎯 Opportunity: DECISION={decision}, OPPORTUNITY={best_opp}")
            
            if decision == 'LONG':
                print(f"   ✅ Correctly parsed LONG decision")
            else:
                print(f"   ❌ ISSUE: Expected LONG, got {decision}")
    
    return parsed_results

def analyze_decision_aggregation(parsed_results):
    """Analyze the decision aggregation logic with the parsed results"""
    print("\n🧮 ANALYZING DECISION AGGREGATION")
    print("=" * 60)
    
    # Simulate the aggregation logic from complete_llm_orchestrator_analysis
    decision_votes = {"LONG": 0, "SHORT": 0, "WAIT": 0, "CLOSE": 0}
    confidence_sum = 0
    confidence_count = 0
    reasoning_parts = []
    
    print("\n📊 STEP-BY-STEP VOTE CALCULATION:")
    
    # Risk Assessment Analysis
    if 'risk_assessment' in parsed_results:
        risk_result = parsed_results['risk_assessment']
        approved = risk_result.get('APPROVED', False)
        risk_score = risk_result.get('RISK_SCORE', 100)
        risk_conf = risk_result.get('CONFIDENCE', 50)
        risk_decision = risk_result.get('DECISION', None)
        
        print(f"\n1. RISK ASSESSMENT:")
        print(f"   Input: DECISION={risk_decision}, APPROVED={approved}, RISK_SCORE={risk_score}, CONFIDENCE={risk_conf}")
        
        if risk_decision in ['LONG', 'SHORT']:
            if approved and risk_score < 70:
                # Strong signal for specific direction
                decision_votes[risk_decision] += 2.0
                reasoning_parts.append(f"Risk: {risk_decision} APPROVED (Score: {risk_score})")
                print(f"   Vote: +2.0 {risk_decision} (Strong approved signal)")
            elif approved:
                # Medium signal for specific direction
                decision_votes[risk_decision] += 1.0
                reasoning_parts.append(f"Risk: {risk_decision} APPROVED (Score: {risk_score})")
                print(f"   Vote: +1.0 {risk_decision} (Medium approved signal)")
            else:
                # Risk rejected but still has directional bias
                decision_votes["WAIT"] += 1.5
                reasoning_parts.append(f"Risk: {risk_decision} REJECTED (Score: {risk_score})")
                print(f"   Vote: +1.5 WAIT (Rejected signal)")
        else:
            print(f"   ❌ No DECISION field found, using fallback logic")
        
        confidence_sum += risk_conf
        confidence_count += 1
    
    # Entry Timing Analysis
    if 'entry_timing' in parsed_results:
        entry_result = parsed_results['entry_timing']
        entry_action = entry_result.get('ACTION', 'WAIT')
        entry_conf = entry_result.get('CONFIDENCE', 50)
        entry_decision = entry_result.get('DECISION', None)
        
        print(f"\n2. ENTRY TIMING:")
        print(f"   Input: DECISION={entry_decision}, ACTION={entry_action}, CONFIDENCE={entry_conf}")
        
        if entry_action == 'ENTER_NOW' and entry_decision in ['LONG', 'SHORT']:
            # Strong signal for specific direction
            decision_votes[entry_decision] += 3.0
            reasoning_parts.append(f"Entry: {entry_decision} NOW")
            print(f"   Vote: +3.0 {entry_decision} (Strong entry signal)")
        elif entry_action == 'ENTER_NOW':
            # Fallback: if no specific direction, add to both (old behavior)
            decision_votes["LONG"] += 1.5
            decision_votes["SHORT"] += 1.5
            reasoning_parts.append("Entry: NOW")
            print(f"   Vote: +1.5 LONG, +1.5 SHORT (Fallback - no direction)")
        elif entry_action == 'WAIT':
            decision_votes["WAIT"] += 1.5
            reasoning_parts.append("Entry: WAIT")
            print(f"   Vote: +1.5 WAIT")
        else:  # ABORT
            decision_votes["WAIT"] += 2.0
            reasoning_parts.append("Entry: ABORT")
            print(f"   Vote: +2.0 WAIT (ABORT)")
        
        confidence_sum += entry_conf
        confidence_count += 1
    
    # Opportunity Scanner Analysis
    if 'opportunity_scanner' in parsed_results:
        opp_result = parsed_results['opportunity_scanner']
        best_opp = opp_result.get('BEST_OPPORTUNITY', 'NONE')
        setup_type = opp_result.get('SETUP_TYPE', 'NONE')
        opp_conf = opp_result.get('CONFIDENCE', 50)
        opp_decision = opp_result.get('DECISION', None)
        
        print(f"\n3. OPPORTUNITY SCANNER:")
        print(f"   Input: DECISION={opp_decision}, OPPORTUNITY={best_opp}, SETUP={setup_type}, CONFIDENCE={opp_conf}")
        
        if opp_decision in ['LONG', 'SHORT']:
            if best_opp != 'NONE' and setup_type in ['BREAKOUT', 'MOMENTUM', 'TREND_CONTINUATION']:
                # Strong signal for specific direction
                decision_votes[opp_decision] += 2.0
                reasoning_parts.append(f"Opportunity: {opp_decision} {setup_type}")
                print(f"   Vote: +2.0 {opp_decision} (Strong opportunity)")
            elif setup_type == 'REVERSAL':
                # Reversal signal for specific direction
                decision_votes[opp_decision] += 1.5
                reasoning_parts.append(f"Opportunity: {opp_decision} {setup_type}")
                print(f"   Vote: +1.5 {opp_decision} (Reversal)")
            else:
                # Weak signal but still directional
                decision_votes[opp_decision] += 0.8
                reasoning_parts.append(f"Opportunity: {opp_decision} WEAK")
                print(f"   Vote: +0.8 {opp_decision} (Weak signal)")
        else:
            print(f"   ❌ No DECISION field found, using fallback logic")
        
        confidence_sum += opp_conf
        confidence_count += 1
    
    print(f"\n📊 FINAL VOTE TOTALS:")
    for decision, votes in decision_votes.items():
        print(f"   {decision}: {votes:.1f}")
    
    return decision_votes, confidence_sum, confidence_count, reasoning_parts

def analyze_final_decision_logic(decision_votes, confidence_sum, confidence_count):
    """Analyze the final decision logic"""
    print("\n🎯 ANALYZING FINAL DECISION LOGIC")
    print("=" * 60)
    
    print(f"Vote Counts: {decision_votes}")
    
    # Apply the ultra-aggressive decision logic
    print(f"\nApplying ULTRA-AGGRESSIVE SCALPING LOGIC:")
    
    if decision_votes["CLOSE"] > 1.0:
        final_decision = "CLOSE"
        print(f"   CLOSE votes ({decision_votes['CLOSE']:.1f}) > 1.0 → CLOSE")
    elif decision_votes["LONG"] > decision_votes["SHORT"]:
        print(f"   LONG ({decision_votes['LONG']:.1f}) > SHORT ({decision_votes['SHORT']:.1f})")
        
        # Check ultra-aggressive threshold (0.3x)
        wait_threshold = decision_votes["WAIT"] * 0.3
        print(f"   WAIT threshold: {decision_votes['WAIT']:.1f} * 0.3 = {wait_threshold:.1f}")
        
        if decision_votes["LONG"] > wait_threshold:
            final_decision = "LONG"
            print(f"   LONG ({decision_votes['LONG']:.1f}) > WAIT threshold ({wait_threshold:.1f}) → LONG")
        elif decision_votes["LONG"] > 0.5:
            final_decision = "LONG"
            print(f"   LONG ({decision_votes['LONG']:.1f}) > 0.5 → LONG")
        else:
            final_decision = "LONG"
            print(f"   Force LONG (avoid WAIT)")
    elif decision_votes["SHORT"] > decision_votes["LONG"]:
        print(f"   SHORT ({decision_votes['SHORT']:.1f}) > LONG ({decision_votes['LONG']:.1f})")
        
        # Check ultra-aggressive threshold (0.3x)
        wait_threshold = decision_votes["WAIT"] * 0.3
        print(f"   WAIT threshold: {decision_votes['WAIT']:.1f} * 0.3 = {wait_threshold:.1f}")
        
        if decision_votes["SHORT"] > wait_threshold:
            final_decision = "SHORT"
            print(f"   SHORT ({decision_votes['SHORT']:.1f}) > WAIT threshold ({wait_threshold:.1f}) → SHORT")
        elif decision_votes["SHORT"] > 0.5:
            final_decision = "SHORT"
            print(f"   SHORT ({decision_votes['SHORT']:.1f}) > 0.5 → SHORT")
        else:
            final_decision = "SHORT"
            print(f"   Force SHORT (avoid WAIT)")
    else:
        print(f"   LONG ({decision_votes['LONG']:.1f}) == SHORT ({decision_votes['SHORT']:.1f})")
        
        if decision_votes["LONG"] + decision_votes["SHORT"] > decision_votes["WAIT"]:
            final_decision = "LONG" if decision_votes["LONG"] >= decision_votes["SHORT"] else "SHORT"
            print(f"   Combined action votes > WAIT → {final_decision}")
        elif decision_votes["LONG"] > 0 or decision_votes["SHORT"] > 0:
            final_decision = "LONG" if decision_votes["LONG"] >= decision_votes["SHORT"] else "SHORT"
            print(f"   Any action signal → {final_decision}")
        else:
            final_decision = "WAIT"
            print(f"   No signals → WAIT (last resort)")
    
    # Calculate confidence
    if confidence_count > 0:
        base_confidence = confidence_sum / confidence_count
        max_votes = max(decision_votes.values())
        total_votes = sum(decision_votes.values())
        consensus_strength = max_votes / total_votes if total_votes > 0 else 0
        final_confidence = min(95, base_confidence + (consensus_strength * 20))
        
        print(f"\nCONFIDENCE CALCULATION:")
        print(f"   Individual confidences sum: {confidence_sum}")
        print(f"   Count: {confidence_count}")
        print(f"   Base confidence (average): {base_confidence:.1f}%")
        print(f"   Max votes: {max_votes:.1f}, Total votes: {total_votes:.1f}")
        print(f"   Consensus strength: {consensus_strength:.2f}")
        print(f"   Consensus boost: {consensus_strength * 20:.1f}%")
        print(f"   Final confidence: {final_confidence:.1f}%")
    else:
        final_confidence = 50.0
        print(f"\nCONFIDENCE: Default 50% (no prompts)")
    
    return final_decision, final_confidence

def main():
    """Run the complete analysis"""
    print("🚀 LLM ORCHESTRATOR DECISION FLOW ANALYSIS")
    print("=" * 70)
    print("Analyzing the specific log scenario:")
    print("- Risk Assessment: DECISION: SHORT, CONFIDENCE: 85%")
    print("- Entry Timing: DECISION: SHORT, ACTION: ENTER_NOW, CONFIDENCE: 75%")
    print("- Opportunity Scanner: DECISION: LONG, CONFIDENCE: 80%")
    print("=" * 70)
    
    # Step 1: Analyze response parsing
    parsed_results = analyze_response_parsing()
    
    # Step 2: Analyze decision aggregation
    decision_votes, confidence_sum, confidence_count, reasoning_parts = analyze_decision_aggregation(parsed_results)
    
    # Step 3: Analyze final decision logic
    final_decision, final_confidence = analyze_final_decision_logic(decision_votes, confidence_sum, confidence_count)
    
    # Step 4: Summary and comparison
    print("\n🎯 FINAL ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"Final Decision: {final_decision}")
    print(f"Final Confidence: {final_confidence:.1f}%")
    print(f"Reasoning: {', '.join(reasoning_parts[:3])}")
    
    print(f"\n📊 COMPARISON TO LOG ISSUE:")
    print(f"Expected from logs: Decision Votes: LONG=0.8, SHORT=3.0, WAIT=2.5")
    print(f"Actual calculated: Decision Votes: LONG={decision_votes['LONG']:.1f}, SHORT={decision_votes['SHORT']:.1f}, WAIT={decision_votes['WAIT']:.1f}")
    
    print(f"\n🔍 ISSUE IDENTIFICATION:")
    
    # Check for parsing issues
    risk_decision = parsed_results.get('risk_assessment', {}).get('DECISION')
    if risk_decision != 'SHORT':
        print(f"❌ PARSING ISSUE: Risk assessment DECISION should be SHORT, got {risk_decision}")
    else:
        print(f"✅ Risk assessment DECISION correctly parsed as SHORT")
    
    entry_decision = parsed_results.get('entry_timing', {}).get('DECISION')
    if entry_decision != 'SHORT':
        print(f"❌ PARSING ISSUE: Entry timing DECISION should be SHORT, got {entry_decision}")
    else:
        print(f"✅ Entry timing DECISION correctly parsed as SHORT")
    
    opp_decision = parsed_results.get('opportunity_scanner', {}).get('DECISION')
    if opp_decision != 'LONG':
        print(f"❌ PARSING ISSUE: Opportunity scanner DECISION should be LONG, got {opp_decision}")
    else:
        print(f"✅ Opportunity scanner DECISION correctly parsed as LONG")
    
    # Check for aggregation issues
    expected_short_votes = 2.0 + 3.0  # Risk (2.0) + Entry (3.0) = 5.0 for SHORT
    expected_long_votes = 2.0  # Opportunity (2.0) for LONG
    
    if abs(decision_votes['SHORT'] - expected_short_votes) > 0.1:
        print(f"❌ AGGREGATION ISSUE: Expected SHORT votes ~{expected_short_votes}, got {decision_votes['SHORT']:.1f}")
    else:
        print(f"✅ SHORT votes correctly aggregated: {decision_votes['SHORT']:.1f}")
    
    if abs(decision_votes['LONG'] - expected_long_votes) > 0.1:
        print(f"❌ AGGREGATION ISSUE: Expected LONG votes ~{expected_long_votes}, got {decision_votes['LONG']:.1f}")
    else:
        print(f"✅ LONG votes correctly aggregated: {decision_votes['LONG']:.1f}")
    
    # Check final decision
    if decision_votes['SHORT'] > decision_votes['LONG'] and final_decision != 'SHORT':
        print(f"❌ DECISION LOGIC ISSUE: SHORT has more votes ({decision_votes['SHORT']:.1f} vs {decision_votes['LONG']:.1f}) but final decision is {final_decision}")
    elif decision_votes['SHORT'] > decision_votes['LONG'] and final_decision == 'SHORT':
        print(f"✅ Final decision correctly follows vote majority: SHORT")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print(f"The system should produce: {final_decision} ({final_confidence:.1f}%)")

if __name__ == "__main__":
    main()
