#!/usr/bin/env python3
"""
Integration test for Dynamic Symbol Scanner
Tests the complete symbol scanner functionality with mock data
"""

import sys
import os
import time
from unittest.mock import Mock, MagicMock

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from symbol_scanner import SymbolScanner, SymbolScannerConfig


class MockExchange:
    """Mock exchange for testing symbol scanner integration"""
    
    def __init__(self):
        self.markets_data = {
            'BTC/USDT:USDT': {'active': True, 'base': 'BTC', 'quote': 'USDT'},
            'ETH/USDT:USDT': {'active': True, 'base': 'ETH', 'quote': 'USDT'},
            'DOGE/USDT:USDT': {'active': True, 'base': 'DOGE', 'quote': 'USDT'},
            'ADA/USDT:USDT': {'active': True, 'base': 'ADA', 'quote': 'USDT'},
            'SOL/USDT:USDT': {'active': True, 'base': 'SOL', 'quote': 'USDT'},
            'MATIC/USDT:USDT': {'active': True, 'base': 'MATIC', 'quote': 'USDT'},
        }
        
        self.price_data = {
            'BTC/USDT:USDT': 45000.0,
            'ETH/USDT:USDT': 3200.0,
            'DOGE/USDT:USDT': 0.08,
            'ADA/USDT:USDT': 0.45,
            'SOL/USDT:USDT': 95.0,
            'MATIC/USDT:USDT': 0.85,
        }
        
        self.volume_data = {
            'BTC/USDT:USDT': 1000000000,
            'ETH/USDT:USDT': 500000000,
            'DOGE/USDT:USDT': 100000000,
            'ADA/USDT:USDT': 80000000,
            'SOL/USDT:USDT': 200000000,
            'MATIC/USDT:USDT': 150000000,
        }
    
    def load_markets(self):
        """Mock load_markets"""
        return self.markets_data
    
    def fetch_ticker(self, symbol):
        """Mock fetch_ticker"""
        price = self.price_data.get(symbol, 100.0)
        volume = self.volume_data.get(symbol, 1000000)
        
        return {
            'last': price,
            'quoteVolume': volume,
            'baseVolume': volume / price
        }
    
    def fetch_order_book(self, symbol, limit=5):
        """Mock fetch_order_book with realistic spreads"""
        price = self.price_data.get(symbol, 100.0)
        
        # Different spread characteristics for different symbols
        if 'BTC' in symbol:
            spread_pct = 0.02  # 0.02% spread for BTC
        elif 'ETH' in symbol:
            spread_pct = 0.03  # 0.03% spread for ETH
        elif 'DOGE' in symbol:
            spread_pct = 0.1   # 0.1% spread for DOGE (higher)
        else:
            spread_pct = 0.05  # 0.05% spread for others
        
        spread = price * (spread_pct / 100)
        bid_price = price - spread / 2
        ask_price = price + spread / 2
        
        # Generate realistic orderbook depth
        base_volume = 100.0
        if 'BTC' in symbol:
            base_volume = 10.0  # Lower volume for BTC due to high price
        elif 'DOGE' in symbol:
            base_volume = 10000.0  # Higher volume for DOGE due to low price
        
        bids = []
        asks = []
        
        for i in range(limit):
            bid_vol = base_volume * (1 + i * 0.5)
            ask_vol = base_volume * (1 + i * 0.5)
            
            bids.append([bid_price - i * spread * 0.1, bid_vol])
            asks.append([ask_price + i * spread * 0.1, ask_vol])
        
        return {'bids': bids, 'asks': asks}
    
    def fetch_trades(self, symbol, limit=50):
        """Mock fetch_trades with realistic trade flow"""
        price = self.price_data.get(symbol, 100.0)
        
        trades = []
        for i in range(min(limit, 30)):
            # Simulate price movement
            price_variation = price * 0.001 * (0.5 - (i % 10) / 10)
            trade_price = price + price_variation
            
            # Simulate buy/sell imbalance
            if 'BTC' in symbol or 'ETH' in symbol:
                side = 'buy' if i % 3 != 0 else 'sell'  # More buys for major coins
            else:
                side = 'buy' if i % 2 == 0 else 'sell'  # Balanced for others
            
            amount = 1.0 + (i % 5)
            
            trades.append({
                'price': trade_price,
                'amount': amount,
                'side': side
            })
        
        return trades


def test_symbol_scanner_basic():
    """Test basic symbol scanner functionality"""
    print("🧪 Testing Basic Symbol Scanner Functionality")
    print("=" * 60)
    
    # Create mock exchange
    mock_exchange = MockExchange()
    
    # Test symbols
    test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT', 'ADA/USDT:USDT']
    
    # Create scanner
    scanner = SymbolScanner(
        market_api=mock_exchange,
        symbols=test_symbols,
        metrics_weights=SymbolScannerConfig.DEFAULT_WEIGHTS
    )
    
    print(f"✓ Scanner initialized with {len(test_symbols)} symbols")
    
    # Test fetching metrics for each symbol
    print("\n📊 Fetching metrics for each symbol:")
    for symbol in test_symbols:
        metrics = scanner.fetch_metrics(symbol)
        if metrics:
            print(f"  {symbol}:")
            print(f"    Price: ${metrics.price:.6f}")
            print(f"    Spread: {metrics.spread_pct:.3f}%")
            print(f"    ATR: {metrics.tick_atr:.6f}")
            print(f"    Flow: {metrics.flow_imbalance:.1f}%")
            print(f"    Depth: {metrics.orderbook_depth:.0f}")
            print(f"    Volume: ${metrics.volume_24h:,.0f}")
        else:
            print(f"  ❌ Failed to fetch metrics for {symbol}")
    
    # Test scoring
    print("\n🎯 Testing symbol scoring:")
    for symbol in test_symbols:
        metrics = scanner.fetch_metrics(symbol)
        if metrics:
            score = scanner.score_symbol(metrics)
            metrics.score = score
            print(f"  {symbol}: {score:.2f}/100")
    
    # Test finding best symbols
    print("\n🏆 Finding best symbols:")
    best_symbols = scanner.find_best(n=len(test_symbols))
    for i, symbol in enumerate(best_symbols):
        metrics = scanner.get_symbol_metrics(symbol)
        score = metrics.score if metrics else 0
        print(f"  {i+1}. {symbol}: {score:.2f}")
    
    print(f"\n✅ Best symbol for trading: {best_symbols[0]}")
    return True


def test_symbol_scanner_config():
    """Test symbol scanner configuration"""
    print("\n🧪 Testing Symbol Scanner Configuration")
    print("=" * 60)
    
    # Test default configuration
    mock_exchange = MockExchange()
    scanner = SymbolScannerConfig.create_scanner(mock_exchange)
    
    print(f"✓ Default scanner created")
    print(f"  Symbols: {len(scanner.symbols)}")
    print(f"  Weights: {scanner.metrics_weights}")
    
    # Test custom weights
    custom_weights = {
        'spread_score': 0.4,    # Prioritize low spread
        'volume_score': 0.3,    # Prioritize high volume
        'depth_score': 0.2,     # Moderate depth importance
        'tick_atr_score': 0.1,  # Lower volatility importance
        'flow_score': 0.0       # Ignore flow imbalance
    }
    
    custom_scanner = SymbolScanner(
        market_api=mock_exchange,
        symbols=SymbolScannerConfig.DEFAULT_SYMBOLS[:4],
        metrics_weights=custom_weights
    )
    
    print(f"\n✓ Custom scanner created with modified weights")
    
    # Compare results
    default_best = scanner.find_best(n=1)
    custom_best = custom_scanner.find_best(n=1)
    
    print(f"  Default weights best: {default_best[0] if default_best else 'None'}")
    print(f"  Custom weights best: {custom_best[0] if custom_best else 'None'}")
    
    return True


def test_symbol_scanner_performance():
    """Test symbol scanner performance"""
    print("\n🧪 Testing Symbol Scanner Performance")
    print("=" * 60)
    
    mock_exchange = MockExchange()
    scanner = SymbolScanner(
        market_api=mock_exchange,
        symbols=list(mock_exchange.markets_data.keys()),
        metrics_weights=SymbolScannerConfig.DEFAULT_WEIGHTS
    )
    
    # Test scan speed
    start_time = time.time()
    best_symbols = scanner.find_best(n=3)
    first_scan_time = time.time() - start_time
    
    print(f"✓ First scan completed in {first_scan_time:.3f} seconds")
    print(f"  Top 3 symbols: {best_symbols}")
    
    # Test cached scan speed
    start_time = time.time()
    cached_symbols = scanner.find_best(n=3)
    cached_scan_time = time.time() - start_time
    
    print(f"✓ Cached scan completed in {cached_scan_time:.3f} seconds")
    if cached_scan_time > 0:
        speedup = first_scan_time / cached_scan_time
        print(f"  Speedup: {speedup:.1f}x faster")
    else:
        print(f"  Speedup: Very fast (cached)")
    
    # Test scan summary
    summary = scanner.get_scan_summary()
    print(f"\n📊 Scan Summary:")
    print(f"  Status: {summary['status']}")
    print(f"  Symbols scanned: {summary['symbols_scanned']}")
    print(f"  Best symbol: {summary.get('best_symbol', 'N/A')}")
    print(f"  Average score: {summary.get('avg_score', 0):.2f}")
    
    return True


def test_symbol_scanner_edge_cases():
    """Test symbol scanner edge cases"""
    print("\n🧪 Testing Symbol Scanner Edge Cases")
    print("=" * 60)
    
    mock_exchange = MockExchange()
    
    # Test with empty symbols list
    try:
        empty_scanner = SymbolScanner(mock_exchange, [], SymbolScannerConfig.DEFAULT_WEIGHTS)
        empty_result = empty_scanner.find_best(n=1)
        print(f"✓ Empty symbols list handled: {empty_result}")
    except Exception as e:
        print(f"❌ Empty symbols list error: {e}")
    
    # Test with invalid symbol
    scanner = SymbolScanner(
        mock_exchange, 
        ['INVALID/SYMBOL:USDT'], 
        SymbolScannerConfig.DEFAULT_WEIGHTS
    )
    
    try:
        invalid_result = scanner.find_best(n=1)
        print(f"✓ Invalid symbol handled: {invalid_result}")
    except Exception as e:
        print(f"❌ Invalid symbol error: {e}")
    
    # Test weight updates
    scanner = SymbolScanner(
        mock_exchange,
        ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
        SymbolScannerConfig.DEFAULT_WEIGHTS
    )
    
    original_best = scanner.find_best(n=1)
    
    # Update weights to prioritize volume heavily
    scanner.update_weights({'volume_score': 0.8, 'spread_score': 0.2})
    updated_best = scanner.find_best(n=1)
    
    print(f"✓ Weight update test:")
    print(f"  Original best: {original_best[0] if original_best else 'None'}")
    print(f"  Updated best: {updated_best[0] if updated_best else 'None'}")
    
    return True


def test_integration_simulation():
    """Simulate the integration with the main trading interface"""
    print("\n🧪 Testing Integration Simulation")
    print("=" * 60)
    
    mock_exchange = MockExchange()
    scanner = SymbolScanner(
        market_api=mock_exchange,
        symbols=list(mock_exchange.markets_data.keys()),
        metrics_weights=SymbolScannerConfig.DEFAULT_WEIGHTS
    )
    
    # Simulate the GUI integration workflow
    print("🤖 Simulating GUI integration workflow:")
    
    # 1. Initial scan
    print("  1. Performing initial scan...")
    best_symbols = scanner.find_best(n=1)
    current_symbol = best_symbols[0] if best_symbols else None
    print(f"     Selected symbol: {current_symbol}")
    
    # 2. Simulate timer ticks (5 scans)
    print("  2. Simulating timer ticks...")
    for i in range(5):
        time.sleep(0.1)  # Small delay to simulate real timing
        
        new_best = scanner.find_best(n=1)
        new_symbol = new_best[0] if new_best else None
        
        if new_symbol != current_symbol:
            print(f"     Tick {i+1}: Symbol changed from {current_symbol} to {new_symbol}")
            current_symbol = new_symbol
        else:
            metrics = scanner.get_symbol_metrics(current_symbol)
            score = metrics.score if metrics else 0
            print(f"     Tick {i+1}: Keeping {current_symbol} (score: {score:.1f})")
    
    # 3. Get final status
    summary = scanner.get_scan_summary()
    print(f"  3. Final status: {summary['symbols_scanned']} symbols, best: {summary.get('best_symbol', 'N/A')}")
    
    print("✅ Integration simulation completed successfully")
    return True


def main():
    """Run all symbol scanner tests"""
    print("🔧 DYNAMIC SYMBOL SCANNER INTEGRATION TESTS")
    print("=" * 80)
    
    tests = [
        test_symbol_scanner_basic,
        test_symbol_scanner_config,
        test_symbol_scanner_performance,
        test_symbol_scanner_edge_cases,
        test_integration_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ ERROR: {e}\n")
    
    print("=" * 80)
    print(f"🎯 SYMBOL SCANNER TESTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SYMBOL SCANNER TESTS PASSED!")
        print("✅ Basic functionality working")
        print("✅ Configuration system working")
        print("✅ Performance optimization working")
        print("✅ Edge cases handled")
        print("✅ GUI integration ready")
        return True
    else:
        print("⚠️ Some symbol scanner tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
