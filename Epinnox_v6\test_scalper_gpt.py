#!/usr/bin/env python3
"""
Test script for ScalperGPT functionality
Tests the new LLM-driven scalper with full trade execution control
"""

import sys
import os
import json

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_trade_instruction_parsing():
    """Test the trade instruction parsing functionality"""
    print("🧪 Testing Trade Instruction Parsing...")
    
    # Mock the parsing function
    def parse_trade_instruction(final_response: str):
        """Parse and validate JSON trade instruction from LLM response"""
        try:
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{[^{}]*\}', final_response, re.DOTALL)
            if not json_match:
                return None
            
            json_str = json_match.group(0)
            trade_instruction = json.loads(json_str)
            
            # Validate required fields
            required_fields = ["ACTION", "QUANTITY", "LEVERAGE", "RISK_PCT", "ORDER_TYPE"]
            for field in required_fields:
                if field not in trade_instruction:
                    return None
            
            # Validate ACTION
            if trade_instruction["ACTION"] not in ["BUY", "SELL", "WAIT"]:
                return None
            
            # Validate and constrain values
            trade_instruction["QUANTITY"] = max(0.0, float(trade_instruction["QUANTITY"]))
            trade_instruction["LEVERAGE"] = max(1, min(200, int(trade_instruction["LEVERAGE"])))
            trade_instruction["RISK_PCT"] = max(0.5, min(5.0, float(trade_instruction["RISK_PCT"])))
            
            return trade_instruction

        except Exception as e:
            print(f"Error parsing: {e}")
            return None
    
    # Test cases
    test_cases = [
        {
            "name": "Valid BUY instruction",
            "response": '''
            {
              "ACTION": "BUY",
              "QUANTITY": 150.0,
              "LEVERAGE": 20,
              "STOP_LOSS": 0.0235,
              "TAKE_PROFIT": 0.0250,
              "RISK_PCT": 2.0,
              "ORDER_TYPE": "MARKET"
            }
            ''',
            "expected": True
        },
        {
            "name": "Valid SELL instruction",
            "response": '''
            {
              "ACTION": "SELL",
              "QUANTITY": 75.5,
              "LEVERAGE": 10,
              "RISK_PCT": 1.5,
              "ORDER_TYPE": "LIMIT"
            }
            ''',
            "expected": True
        },
        {
            "name": "Invalid ACTION",
            "response": '''
            {
              "ACTION": "INVALID",
              "QUANTITY": 100.0,
              "LEVERAGE": 15,
              "RISK_PCT": 2.0,
              "ORDER_TYPE": "MARKET"
            }
            ''',
            "expected": False
        },
        {
            "name": "Missing required field",
            "response": '''
            {
              "ACTION": "BUY",
              "QUANTITY": 100.0,
              "LEVERAGE": 15
            }
            ''',
            "expected": False
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        result = parse_trade_instruction(test_case["response"])
        success = (result is not None) == test_case["expected"]
        
        if success:
            print(f"✅ {test_case['name']}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_case['name']}: FAILED")
    
    print(f"\n📊 Trade Parsing Tests: {passed}/{total} passed")
    return passed == total

def test_market_data_enrichment():
    """Test the market data enrichment functionality"""
    print("\n🧪 Testing Market Data Enrichment...")
    
    # Mock market data structure
    mock_market_data = {
        'best_bid': 0.17234,
        'best_ask': 0.17236,
        'spread': 0.00002,
        'spread_pct': 0.012,
        'top_5_bids': [
            (0.17234, 1500.0),
            (0.17233, 2200.0),
            (0.17232, 1800.0),
            (0.17231, 3000.0),
            (0.17230, 2500.0)
        ],
        'top_5_asks': [
            (0.17236, 1600.0),
            (0.17237, 2100.0),
            (0.17238, 1900.0),
            (0.17239, 2800.0),
            (0.17240, 2400.0)
        ],
        'tick_atr': 0.00015,
        'trade_flow_imbalance': 12.5,
        'volume_momentum': 8.3,
        'data_latency_ms': 45.0
    }
    
    # Test data structure
    required_fields = [
        'best_bid', 'best_ask', 'spread', 'spread_pct',
        'top_5_bids', 'top_5_asks', 'tick_atr',
        'trade_flow_imbalance', 'volume_momentum', 'data_latency_ms'
    ]
    
    all_fields_present = all(field in mock_market_data for field in required_fields)
    
    if all_fields_present:
        print("✅ Market data structure: PASSED")
        print(f"   • Spread: ${mock_market_data['spread']:.6f} ({mock_market_data['spread_pct']:.3f}%)")
        print(f"   • Tick ATR: ${mock_market_data['tick_atr']:.6f}")
        print(f"   • Trade Flow: {mock_market_data['trade_flow_imbalance']:+.1f}%")
        print(f"   • Volume Momentum: {mock_market_data['volume_momentum']:+.1f}%")
        print(f"   • Data Latency: {mock_market_data['data_latency_ms']:.0f}ms")
        return True
    else:
        print("❌ Market data structure: FAILED")
        return False

def test_ensemble_analysis():
    """Test the adaptive ensemble analysis"""
    print("\n🧪 Testing Adaptive Ensemble Analysis...")
    
    # Mock ML model decisions and confidences
    ml_decisions = ["LONG", "LONG", "SHORT", "WAIT", "LONG", "LONG", "WAIT", "SHORT"]
    ml_confidences = [75.2, 68.9, 82.1, 55.0, 71.3, 79.8, 60.2, 77.5]
    
    # Test ensemble calculations
    vote_counts = {"LONG": 0, "SHORT": 0, "WAIT": 0}
    for decision in ml_decisions:
        if decision in vote_counts:
            vote_counts[decision] += 1
    
    majority_vote = max(vote_counts, key=vote_counts.get)
    avg_confidence = sum(ml_confidences) / len(ml_confidences)
    
    # Calculate weighted score
    weighted_score = 0.0
    for decision, confidence in zip(ml_decisions, ml_confidences):
        score = 1 if decision == "LONG" else (-1 if decision == "SHORT" else 0)
        weighted_score += score * (confidence / 100)
    weighted_score /= len(ml_decisions)
    
    consensus_strength = (max(vote_counts.values()) / len(ml_decisions)) * 100
    
    print(f"✅ Ensemble Analysis: PASSED")
    print(f"   • Majority Vote: {majority_vote}")
    print(f"   • Average Confidence: {avg_confidence:.1f}%")
    print(f"   • Weighted Score: {weighted_score:+.2f}")
    print(f"   • Consensus Strength: {consensus_strength:.1f}%")
    print(f"   • Vote Distribution: LONG={vote_counts['LONG']}, SHORT={vote_counts['SHORT']}, WAIT={vote_counts['WAIT']}")
    
    return True

def test_scalper_prompt_building():
    """Test the ScalperGPT prompt building"""
    print("\n🧪 Testing ScalperGPT Prompt Building...")
    
    # Mock prompt components
    symbol = "DOGE/USDT:USDT"
    current_price = 0.17235
    
    # Build a sample prompt
    prompt_template = f"""You are ScalperGPT, specialized in ultra-short trades (5–30s).
Provide a strictly binary recommendation: BUY, SELL, or WAIT, with one-sentence justification.

═══════════════════════════════════════════════════════════════════════════════
📊 SCALP ANALYSIS - ENRICHED MARKET DATA
═══════════════════════════════════════════════════════════════════════════════
🏷️ SYMBOL: {symbol}
💰 CURRENT PRICE: ${current_price:.6f}

Provide your final recommendation in strict JSON format."""
    
    # Test prompt structure
    required_elements = [
        "ScalperGPT",
        "ultra-short trades",
        "BUY, SELL, or WAIT",
        "SCALP ANALYSIS",
        "ENRICHED MARKET DATA",
        "JSON format"
    ]
    
    all_elements_present = all(element in prompt_template for element in required_elements)
    
    if all_elements_present:
        print("✅ ScalperGPT Prompt: PASSED")
        print(f"   • Prompt length: {len(prompt_template)} characters")
        print("   • Contains all required elements")
        return True
    else:
        print("❌ ScalperGPT Prompt: FAILED")
        return False

def main():
    """Run all ScalperGPT tests"""
    print("🤖 SCALPER GPT FUNCTIONALITY TESTS")
    print("=" * 50)
    
    tests = [
        test_trade_instruction_parsing,
        test_market_data_enrichment,
        test_ensemble_analysis,
        test_scalper_prompt_building
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 OVERALL RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! ScalperGPT is ready for deployment.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
