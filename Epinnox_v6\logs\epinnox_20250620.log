2025-06-20 00:02:20,587 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:02:20,603 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:02:20,604 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:02:20,606 - main - INFO - Performance monitoring initialized
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:02:20,617 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:02:25,454 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:02:26,356 - websocket - INFO - Websocket connected
2025-06-20 00:02:28,640 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:02:28,641 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:02:28,641 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:02:28,642 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:02:28,646 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:02:30,734 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:02:30,735 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:02:30,739 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:02:30,742 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:02:30,743 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:02:30,754 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:02:30,755 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:02:30,755 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:02:30,761 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:02:30,765 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:11:47,033 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:11:47,047 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:11:47,048 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:11:47,049 - main - INFO - Performance monitoring initialized
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:11:47,058 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:11:52,335 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:11:53,254 - websocket - INFO - Websocket connected
2025-06-20 00:11:55,890 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:11:55,890 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:11:55,892 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:11:55,893 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:11:55,898 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,929 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,930 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:11:57,931 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:11:57,933 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:11:57,934 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:11:57,942 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:11:57,943 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:11:57,944 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:11:57,948 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 00:11:57,952 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 08:23:44,214 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:23:44,245 - main - INFO - Performance monitoring initialized
2025-06-20 08:23:44,258 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:23:44,259 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:23:44,260 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:23:55,818 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:23:56,752 - websocket - INFO - Websocket connected
2025-06-20 08:23:59,900 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:23:59,901 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:23:59,901 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:23:59,902 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:23:59,908 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,055 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,056 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:24:02,056 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:24:02,060 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:24:02,060 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:24:02,092 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:24:02,094 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:24:02,094 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:24:02,098 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,102 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,171 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 08:25:05,899 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:25:05,917 - main - INFO - Performance monitoring initialized
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:25:05,928 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:25:13,075 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:25:13,963 - websocket - INFO - Websocket connected
2025-06-20 08:25:16,800 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:25:16,805 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:25:16,805 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:25:16,806 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:25:16,811 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:25:18,882 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:25:18,884 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,885 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,887 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:25:18,887 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:25:18,890 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:25:18,890 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:25:18,898 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:25:18,901 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:25:18,902 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:25:18,909 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,912 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,970 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 09:19:16,806 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-20 10:33:59,743 - main - INFO - Epinnox v6 starting up...
2025-06-20 10:33:59,804 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 10:33:59,805 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 10:33:59,809 - main - INFO - Performance monitoring initialized
2025-06-20 10:33:59,832 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 10:33:59,833 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 10:33:59,835 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 10:34:27,417 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:34:28,188 - websocket - INFO - Websocket connected
2025-06-20 10:34:30,405 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 10:34:30,407 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 10:34:30,409 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 10:34:30,420 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 10:34:30,441 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 10:34:32,561 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 10:34:32,563 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,566 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,570 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 10:34:32,574 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 10:34:32,582 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 10:34:32,582 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 10:34:32,610 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 10:34:32,630 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 10:34:32,631 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 10:34:32,641 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,653 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,756 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 10:48:59,366 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:05,490 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:05,490 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 53.08 (spread: 0.000%, atr: 0.025263, depth: 35335)
2025-06-20 10:49:05,514 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:05,521 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:18,620 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:24,367 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:24,368 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.84 (spread: 0.017%, atr: 0.000000, depth: 3019981)
2025-06-20 10:49:24,377 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:28,383 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:34,620 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:34,627 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.07 (spread: 0.017%, atr: 0.000000, depth: 5067876)
2025-06-20 10:49:34,643 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:38,379 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:43,884 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:43,893 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 52.76 (spread: 0.009%, atr: 0.000000, depth: 3295931)
2025-06-20 10:49:43,900 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:49,031 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:55,535 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:55,537 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.93 (spread: 0.009%, atr: 0.000000, depth: 7496739)
2025-06-20 10:49:55,562 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:58,396 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:04,285 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:04,285 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 59.11 (spread: 0.017%, atr: 0.000000, depth: 7144241)
2025-06-20 10:50:04,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:08,404 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:14,305 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:14,308 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 56.98 (spread: 0.026%, atr: 0.000000, depth: 6628940)
2025-06-20 10:50:14,318 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:18,617 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:24,232 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:24,236 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.34 (spread: 0.017%, atr: 0.000000, depth: 5308213)
2025-06-20 10:50:24,248 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:28,400 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:34,166 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:34,166 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.82 (spread: 0.009%, atr: 0.000000, depth: 11127428)
2025-06-20 10:50:34,182 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:38,401 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:44,101 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:44,101 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.56 (spread: 0.009%, atr: 0.000000, depth: 10954232)
2025-06-20 10:50:44,109 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:49,228 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:55,061 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:55,062 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.08 (spread: 0.026%, atr: 0.000000, depth: 6136076)
2025-06-20 10:50:55,078 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:58,407 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:04,591 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:04,592 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.30 (spread: 0.009%, atr: 0.000000, depth: 4797782)
2025-06-20 10:51:04,595 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:08,414 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:14,248 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:14,248 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.50 (spread: 0.009%, atr: 0.000000, depth: 7606578)
2025-06-20 10:51:14,272 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:18,665 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:24,824 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:24,824 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.47 (spread: 0.009%, atr: 0.000000, depth: 1565993)
2025-06-20 10:51:24,839 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:28,418 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:33,909 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:33,916 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 47.38 (spread: 0.009%, atr: 0.000000, depth: 1780332)
2025-06-20 10:51:33,933 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:38,411 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:44,150 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:44,150 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.26 (spread: 0.017%, atr: 0.000000, depth: 4143947)
2025-06-20 10:51:44,165 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:48,656 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:54,272 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:54,273 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.36 (spread: 0.017%, atr: 0.000000, depth: 4881429)
2025-06-20 10:51:54,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:58,416 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:52:03,980 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:52:03,980 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.79 (spread: 0.009%, atr: 0.000000, depth: 5076147)
2025-06-20 10:52:04,004 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
