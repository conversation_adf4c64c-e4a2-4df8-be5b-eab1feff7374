2025-06-20 00:02:20,587 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:02:20,603 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:02:20,604 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:02:20,606 - main - INFO - Performance monitoring initialized
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:02:20,617 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:02:25,454 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:02:26,356 - websocket - INFO - Websocket connected
2025-06-20 00:02:28,640 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:02:28,641 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:02:28,641 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:02:28,642 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:02:28,646 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:02:30,734 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:02:30,735 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:02:30,739 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:02:30,742 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:02:30,743 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:02:30,754 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:02:30,755 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:02:30,755 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:02:30,761 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:02:30,765 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:11:47,033 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:11:47,047 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:11:47,048 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:11:47,049 - main - INFO - Performance monitoring initialized
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:11:47,058 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:11:52,335 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:11:53,254 - websocket - INFO - Websocket connected
2025-06-20 00:11:55,890 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:11:55,890 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:11:55,892 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:11:55,893 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:11:55,898 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,929 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,930 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:11:57,931 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:11:57,933 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:11:57,934 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:11:57,942 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:11:57,943 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:11:57,944 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:11:57,948 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 00:11:57,952 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 08:23:44,214 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:23:44,245 - main - INFO - Performance monitoring initialized
2025-06-20 08:23:44,258 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:23:44,259 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:23:44,260 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:23:55,818 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:23:56,752 - websocket - INFO - Websocket connected
2025-06-20 08:23:59,900 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:23:59,901 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:23:59,901 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:23:59,902 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:23:59,908 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,055 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,056 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:24:02,056 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:24:02,060 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:24:02,060 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:24:02,092 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:24:02,094 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:24:02,094 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:24:02,098 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,102 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,171 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 08:25:05,899 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:25:05,917 - main - INFO - Performance monitoring initialized
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:25:05,928 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:25:13,075 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:25:13,963 - websocket - INFO - Websocket connected
2025-06-20 08:25:16,800 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:25:16,805 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:25:16,805 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:25:16,806 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:25:16,811 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:25:18,882 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:25:18,884 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,885 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,887 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:25:18,887 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:25:18,890 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:25:18,890 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:25:18,898 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:25:18,901 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:25:18,902 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:25:18,909 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,912 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,970 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 09:19:16,806 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-20 10:33:59,743 - main - INFO - Epinnox v6 starting up...
2025-06-20 10:33:59,804 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 10:33:59,805 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 10:33:59,809 - main - INFO - Performance monitoring initialized
2025-06-20 10:33:59,832 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 10:33:59,833 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 10:33:59,835 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 10:34:27,417 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:34:28,188 - websocket - INFO - Websocket connected
2025-06-20 10:34:30,405 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 10:34:30,407 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 10:34:30,409 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 10:34:30,420 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 10:34:30,441 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 10:34:32,561 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 10:34:32,563 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,566 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,570 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 10:34:32,574 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 10:34:32,582 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 10:34:32,582 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 10:34:32,610 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 10:34:32,630 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 10:34:32,631 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 10:34:32,641 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,653 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,756 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 10:48:59,366 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:05,490 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:05,490 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 53.08 (spread: 0.000%, atr: 0.025263, depth: 35335)
2025-06-20 10:49:05,514 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:05,521 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:18,620 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:24,367 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:24,368 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.84 (spread: 0.017%, atr: 0.000000, depth: 3019981)
2025-06-20 10:49:24,377 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:28,383 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:34,620 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:34,627 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.07 (spread: 0.017%, atr: 0.000000, depth: 5067876)
2025-06-20 10:49:34,643 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:38,379 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:43,884 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:43,893 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 52.76 (spread: 0.009%, atr: 0.000000, depth: 3295931)
2025-06-20 10:49:43,900 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:49,031 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:55,535 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:55,537 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.93 (spread: 0.009%, atr: 0.000000, depth: 7496739)
2025-06-20 10:49:55,562 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:58,396 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:04,285 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:04,285 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 59.11 (spread: 0.017%, atr: 0.000000, depth: 7144241)
2025-06-20 10:50:04,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:08,404 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:14,305 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:14,308 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 56.98 (spread: 0.026%, atr: 0.000000, depth: 6628940)
2025-06-20 10:50:14,318 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:18,617 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:24,232 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:24,236 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.34 (spread: 0.017%, atr: 0.000000, depth: 5308213)
2025-06-20 10:50:24,248 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:28,400 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:34,166 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:34,166 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.82 (spread: 0.009%, atr: 0.000000, depth: 11127428)
2025-06-20 10:50:34,182 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:38,401 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:44,101 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:44,101 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.56 (spread: 0.009%, atr: 0.000000, depth: 10954232)
2025-06-20 10:50:44,109 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:49,228 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:55,061 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:55,062 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.08 (spread: 0.026%, atr: 0.000000, depth: 6136076)
2025-06-20 10:50:55,078 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:58,407 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:04,591 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:04,592 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.30 (spread: 0.009%, atr: 0.000000, depth: 4797782)
2025-06-20 10:51:04,595 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:08,414 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:14,248 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:14,248 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.50 (spread: 0.009%, atr: 0.000000, depth: 7606578)
2025-06-20 10:51:14,272 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:18,665 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:24,824 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:24,824 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.47 (spread: 0.009%, atr: 0.000000, depth: 1565993)
2025-06-20 10:51:24,839 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:28,418 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:33,909 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:33,916 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 47.38 (spread: 0.009%, atr: 0.000000, depth: 1780332)
2025-06-20 10:51:33,933 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:38,411 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:44,150 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:44,150 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.26 (spread: 0.017%, atr: 0.000000, depth: 4143947)
2025-06-20 10:51:44,165 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:48,656 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:54,272 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:54,273 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.36 (spread: 0.017%, atr: 0.000000, depth: 4881429)
2025-06-20 10:51:54,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:58,416 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:52:03,980 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:52:03,980 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.79 (spread: 0.009%, atr: 0.000000, depth: 5076147)
2025-06-20 10:52:04,004 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 11:17:15,634 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-20 13:32:55,016 - main - INFO - Epinnox v6 starting up...
2025-06-20 13:32:55,034 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 13:32:55,034 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 13:32:55,034 - main - INFO - Performance monitoring initialized
2025-06-20 13:32:55,050 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 13:32:55,051 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 13:32:55,053 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 13:33:02,224 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:33:02,950 - websocket - INFO - Websocket connected
2025-06-20 13:33:04,652 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 13:33:04,653 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 13:33:04,653 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 13:33:04,654 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 13:33:04,659 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 13:33:06,788 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 13:33:06,788 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 13:33:06,789 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 13:33:06,789 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 13:33:06,789 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 13:33:06,794 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 13:33:06,795 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 13:33:06,814 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 13:33:06,817 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 13:33:06,817 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 13:33:06,825 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_133306_a3efe816
2025-06-20 13:33:06,827 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_133306_a3efe816
2025-06-20 13:33:06,892 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 13:34:07,203 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:34:10,707 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:35:14,213 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,214 - core.llm_orchestrator - ERROR - Error in risk assessment: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,216 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,221 - core.llm_orchestrator - ERROR - Error in strategy adaptation: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,223 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,223 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.01s - 5 prompts executed
2025-06-20 13:35:29,225 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:35:44,253 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:44,254 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:44,255 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 2 prompts executed
2025-06-20 13:35:52,848 - core.llm_orchestrator - ERROR - Error in emergency response: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,431 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,434 - core.llm_orchestrator - ERROR - Error in risk assessment: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,436 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,437 - core.llm_orchestrator - ERROR - Error in strategy adaptation: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,439 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,440 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.01s - 5 prompts executed
2025-06-20 13:37:41,412 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:37:56,820 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:56,822 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:56,823 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 2 prompts executed
2025-06-20 13:41:44,521 - main - INFO - Epinnox v6 starting up...
2025-06-20 13:41:44,537 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 13:41:44,537 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 13:41:44,538 - main - INFO - Performance monitoring initialized
2025-06-20 13:41:44,547 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 13:41:44,547 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 13:41:44,548 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 13:41:50,447 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:41:51,166 - websocket - INFO - Websocket connected
2025-06-20 13:41:53,064 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 13:41:53,065 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 13:41:53,065 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 13:41:53,066 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 13:41:53,072 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 13:41:55,136 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 13:41:55,136 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 13:41:55,137 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 13:41:55,139 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 13:41:55,139 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 13:41:55,143 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 13:41:55,144 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 13:41:55,154 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 13:41:55,155 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 13:41:55,155 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 13:41:55,161 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_134155_f43dbd55
2025-06-20 13:41:55,164 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_134155_f43dbd55
2025-06-20 13:41:55,223 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 13:42:35,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:35,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:42:35,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:42:35,166 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:50,604 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:42:50,606 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:42:50,606 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:42:50,607 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:42:50,607 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:50,608 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:50,608 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:42:50,609 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:54,448 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 465 chars
2025-06-20 13:42:54,449 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a bullish trend with increasing volume and rising prices. However, the recent spike in price is accompanied by an increase in volatility which suggests potential overbought conditions that ...
2025-06-20 13:42:54,449 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 123, Total: 245
2025-06-20 13:42:54,450 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:54,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:54,452 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162586/$0.162592
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161773
Resistance: $0.163399
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:42:54,453 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:59,487 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 828 chars
2025-06-20 13:42:59,488 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:42:59,488 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 197, Total: 722
2025-06-20 13:42:59,489 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:59,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:59,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:42:59,491 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:05,752 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 847 chars
2025-06-20 13:43:05,753 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, suggesting the need for adjustment despite normal market volatility levels. A risk reduction of half will decrease potential losses while maintai...
2025-06-20 13:43:05,754 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 276, Total: 843
2025-06-20 13:43:05,757 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 0.5x
2025-06-20 13:43:05,757 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 0.5x, Hold time 8min
2025-06-20 13:43:05,758 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:05,759 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:05,759 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162586 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:43:05,760 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:11,620 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 945 chars
2025-06-20 13:43:11,621 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum being neutral but not trending strongly in any direction t...
2025-06-20 13:43:11,621 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 242, Total: 894
2025-06-20 13:43:11,623 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 36.46s - 5 prompts executed
2025-06-20 13:43:20,408 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:20,408 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:43:20,409 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:43:20,410 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:43:23,958 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:43:23,959 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:23,959 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:23,960 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:43:23,960 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:29,031 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 911 chars
2025-06-20 13:43:29,031 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-20 13:43:29,032 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 212, Total: 334
2025-06-20 13:43:29,033 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:29,033 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:29,035 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162524/$0.162525
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161711
Resistance: $0.163337
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:43:29,036 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:35,220 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1202 chars
2025-06-20 13:43:35,221 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, there is an upcoming resistance level at $0.163337 which could be challenging for bullish ...
2025-06-20 13:43:35,221 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 278, Total: 803
2025-06-20 13:43:35,222 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162524 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:40,033 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 649 chars
2025-06-20 13:43:40,034 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking JSON indicates a high-confidence setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budge...
2025-06-20 13:43:40,034 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 171, Total: 823
2025-06-20 13:43:40,034 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 19.63s - 4 prompts executed
2025-06-20 13:43:50,169 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:50,169 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:50,170 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162530/$0.162531
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161717
Resistance: $0.163343
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:43:50,172 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:55,211 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 716 chars
2025-06-20 13:43:55,212 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:43:55,212 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 183, Total: 708
2025-06-20 13:43:55,213 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 5.05s - 1 prompts executed
2025-06-20 13:44:05,400 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:05,401 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:44:05,402 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:44:05,402 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:44:09,109 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:44:09,109 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:09,109 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:09,110 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:44:09,110 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:13,996 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 877 chars
2025-06-20 13:44:13,997 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within three months, suggesting an immine...
2025-06-20 13:44:13,997 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 202, Total: 324
2025-06-20 13:44:13,998 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:13,999 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:13,999 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162541/$0.162542
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161728
Resistance: $0.163354
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:44:14,000 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:19,182 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 874 chars
2025-06-20 13:44:19,183 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-20 13:44:19,183 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 204, Total: 729
2025-06-20 13:44:19,187 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 13.79s - 3 prompts executed
2025-06-20 13:44:35,169 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:35,170 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:35,170 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162586/$0.162587
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161773
Resistance: $0.163399
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:44:35,171 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:40,934 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1071 chars
2025-06-20 13:44:40,936 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-20 13:44:40,936 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 238, Total: 763
2025-06-20 13:44:40,937 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:45,941 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 778 chars
2025-06-20 13:44:45,942 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy is showing a declining win rate and poor recent performance, which indicates that the market regime might be changing. Given this uncertainty (Regime Confidence at 50%) combined with normal volatility ...
2025-06-20 13:44:45,942 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 188, Total: 755
2025-06-20 13:44:45,943 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 13:44:45,944 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:45,944 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:45,946 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162586 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:44:45,947 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:50,808 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 615 chars
2025-06-20 13:44:50,808 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget allows...
2025-06-20 13:44:50,809 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 172, Total: 824
2025-06-20 13:44:50,810 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 15.64s - 3 prompts executed
2025-06-20 13:45:05,538 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:05,539 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:45:05,539 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:45:05,541 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:09,000 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 117 chars
2025-06-20 13:45:09,001 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}...
2025-06-20 13:45:09,001 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 67, Total: 672
2025-06-20 13:45:09,002 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:45:09,002 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:09,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:09,003 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:45:09,004 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:13,619 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 686 chars
2025-06-20 13:45:13,620 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent downward trend in Bitcoin's price over the past week, with volume increasing as sellers step up. Technical indicators such as RSI and Moving Averages are signaling an oversold cond...
2025-06-20 13:45:13,620 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 180, Total: 302
2025-06-20 13:45:13,621 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:13,622 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:13,622 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162543/$0.162544
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161730
Resistance: $0.163356
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:13,623 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:19,642 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 940 chars
2025-06-20 13:45:19,642 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:45:19,643 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 259, Total: 784
2025-06-20 13:45:19,644 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 1 column 179 (char 178)
2025-06-20 13:45:19,645 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 14.11s - 3 prompts executed
2025-06-20 13:45:35,146 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:35,147 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:35,147 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162702/$0.162703
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161888
Resistance: $0.163516
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:35,149 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:40,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 821 chars
2025-06-20 13:45:40,188 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:45:40,190 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 188, Total: 713
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162702 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:45:40,192 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:45,189 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 702 chars
2025-06-20 13:45:45,190 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The best opportunity according to the JSON data is DOGE/USDT, which has a high setup quality with clear breakout patterns and volume confirmation within an optimal time horizon of 5-15 minutes. Given its alignment with tre...
2025-06-20 13:45:45,191 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 184, Total: 836
2025-06-20 13:45:45,192 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.05s - 2 prompts executed
2025-06-20 13:45:50,488 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:54,064 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:45:54,065 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:45:54,065 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:45:54,066 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:45:54,067 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:54,068 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:54,068 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:45:54,069 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:57,833 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 457 chars
2025-06-20 13:45:57,834 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volume but low price stability. Historical trends show that similar patterns have led to significant correctio...
2025-06-20 13:45:57,834 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 119, Total: 241
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162663/$0.162664
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161850
Resistance: $0.163476
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:57,837 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:04,616 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1178 chars
2025-06-20 13:46:04,616 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:46:04,617 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 311, Total: 836
2025-06-20 13:46:04,619 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-06-20 13:46:04,620 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 14.13s - 3 prompts executed
2025-06-20 13:46:20,552 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:20,554 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:46:20,554 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:46:20,556 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:24,074 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:46:24,074 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:46:24,076 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:46:24,077 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:46:24,078 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:24,079 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:24,080 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162664/$0.162665
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161851
Resistance: $0.163477
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:46:24,081 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:30,483 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1034 chars
2025-06-20 13:46:30,483 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price range. Although there is not an immediate signal from technical analysis or ML ensemble indicating s...
2025-06-20 13:46:30,484 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 275, Total: 800
2025-06-20 13:46:30,485 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-06-20 13:46:30,486 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 9.93s - 2 prompts executed
2025-06-20 13:46:35,226 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:35,227 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:35,227 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:46:35,229 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:40,299 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 805 chars
2025-06-20 13:46:40,300 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 70%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy has a win rate trending downwards, which indicates that the market regime might be shifting. Although there's no significant increase in volatility or drawdown level and recent performance is poor but ...
2025-06-20 13:46:40,300 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 194, Total: 761
2025-06-20 13:46:40,301 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 13:46:40,301 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162631 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:45,236 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 653 chars
2025-06-20 13:46:45,237 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but stable enough for scalping opportunitie...
2025-06-20 13:46:45,237 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 173, Total: 825
2025-06-20 13:46:45,239 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.01s - 2 prompts executed
2025-06-20 13:46:50,192 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:50,193 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:50,193 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:46:50,194 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:54,550 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 485 chars
2025-06-20 13:46:54,550 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within two weeks, suggesting an imminent ...
2025-06-20 13:46:54,552 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-20 13:46:54,553 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:54,553 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:54,554 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162649/$0.162664
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161836
Resistance: $0.163462
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:46:54,554 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:00,639 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 954 chars
2025-06-20 13:47:00,639 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no significant volume spikes, suggesting stability. Although the price is near key support levels and momentum remains neutral, there's an opportunity to capit...
2025-06-20 13:47:00,640 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 266, Total: 791
2025-06-20 13:47:00,641 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 1 column 192 (char 191)
2025-06-20 13:47:00,642 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.45s - 2 prompts executed
2025-06-20 13:47:05,179 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:05,180 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:47:05,181 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:47:05,182 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:08,674 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:47:08,674 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:47:08,675 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:47:08,676 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:47:08,676 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 3.50s - 1 prompts executed
2025-06-20 13:47:20,140 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:20,141 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:20,142 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162736/$0.162737
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161922
Resistance: $0.163550
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:47:20,144 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:25,260 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 794 chars
2025-06-20 13:47:25,261 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 65%
TAKE_PROFIT: N/A (as we are waiting for a market entry)
STOP_LOSS: N/A (same reason as take profit percentage is not applicable here)
EXPLANATION: The current signal analysis indicates that the technical indicators and volume flow data do not strongly suggest an immedi...
2025-06-20 13:47:25,262 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 189, Total: 714
2025-06-20 13:47:25,263 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 5.12s - 1 prompts executed
2025-06-20 13:47:35,176 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:47:50,139 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:50,140 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:47:50,141 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:47:50,142 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:53,712 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:47:53,712 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:47:53,713 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:47:53,714 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:47:53,714 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:53,715 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:53,716 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:47:53,716 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:58,851 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 921 chars
2025-06-20 13:47:58,852 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may begin to decline, as indicated by...
2025-06-20 13:47:58,852 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 216, Total: 338
2025-06-20 13:47:58,853 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:58,853 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:58,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162668/$0.162669
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161855
Resistance: $0.163481
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:47:58,854 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:03,707 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 736 chars
2025-06-20 13:48:03,708 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:48:03,708 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 171, Total: 696
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162668 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:48:03,712 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:08,447 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 611 chars
2025-06-20 13:48:08,447 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but stable enough for scalping opportunitie...
2025-06-20 13:48:08,449 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 158, Total: 810
2025-06-20 13:48:08,450 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 18.31s - 4 prompts executed
2025-06-20 13:48:09,173 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:48:09,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:48:09,174 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🚨 EMERGENCY RESPONSE SYSTEM - IMMEDIATE ACTION REQUIRED

⚠️ EMERGENCY FLAGS: MANUAL_EMERGENCY_STOP
💰 ACCOUNT: $50.00 | RISK: $0.00
📊 POSITIONS (0):
• No open positions

📈 MARKET CONDITIONS:
Price: $0.162669 | Spread: 0.000%
Volume: NORMAL | Volatility: NORMAL

🎯 EMERGENCY PROTOCOLS:
- CLOSE_ALL: Close all positions immediately (market orders)
- CLOSE_LOSING: Close only losing positions
- HEDGE: Open hedge positions to reduce risk
- MONITOR: Continue monitoring, no immediate action
- REDUCE_SIZE:...
2025-06-20 13:48:09,175 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:13,359 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 460 chars
2025-06-20 13:48:13,360 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: MONITOR
CONFIDENCE: 85%
TAKE_PROFIT: N/A (No positions to profit from)
STOP_LOSS: N/A (No open positions at risk of loss)
EXPLANATION: The market conditions are stable with normal price and volume, indicating no immediate threat. There are currently no active positions which eliminates the...
2025-06-20 13:48:13,361 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 469, Completion: 123, Total: 592
2025-06-20 13:48:13,362 - core.llm_response_parsers - WARNING - No JSON found in emergency response, using fallback
2025-06-20 14:02:04,157 - main - INFO - Epinnox v6 starting up...
2025-06-20 14:02:04,173 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 14:02:04,174 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 14:02:04,174 - main - INFO - Performance monitoring initialized
2025-06-20 14:02:04,181 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 14:02:04,182 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 14:02:04,183 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 14:02:10,246 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 14:02:10,962 - websocket - INFO - Websocket connected
2025-06-20 14:02:12,732 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 14:02:12,732 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 14:02:12,733 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 14:02:12,735 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 14:02:12,741 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 14:02:14,796 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 14:02:14,797 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 14:02:14,797 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 14:02:14,798 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 14:02:14,799 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 14:02:14,801 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 14:02:14,802 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 14:02:14,810 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 14:02:14,811 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 14:02:14,812 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 14:02:14,821 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_140214_fd9b433a
2025-06-20 14:02:14,822 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_140214_fd9b433a
2025-06-20 14:02:14,882 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 14:29:28,836 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:28,836 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 14:29:28,837 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 14:29:28,837 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:32,508 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 14:29:32,508 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 14:29:32,509 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 14:29:32,510 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 14:29:32,510 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:37,382 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 801 chars
2025-06-20 14:29:37,383 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with its price trending upwards despite lackluster fundamentals and increasing regulatory scrutiny. Historical volatility is hig...
2025-06-20 14:29:37,384 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 192, Total: 314
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.161780/$0.161781
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.160971
Resistance: $0.162589
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 14:29:37,388 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:42,668 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 885 chars
2025-06-20 14:29:42,669 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, despite this balance and favorable spread conditions, there is still neutrality from techn...
2025-06-20 14:29:42,670 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 210, Total: 735
2025-06-20 14:29:42,673 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:42,673 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:42,674 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 14:29:42,675 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:48,895 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1084 chars
2025-06-20 14:29:48,896 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy has a win rate trending downwards, which is not ideal. Although the drawdown level and market volatility are normal, recent performance indicates poor results with an average loss of -0.30%. To mitigat...
2025-06-20 14:29:48,896 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 263, Total: 830
2025-06-20 14:29:48,897 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 14:29:48,899 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:48,899 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:48,900 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.161780 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 14:29:48,900 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:54,210 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 793 chars
2025-06-20 14:29:54,210 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-confidence setup for DOGE/USDT with an identified breakout pattern and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk bud...
2025-06-20 14:29:54,211 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 196, Total: 848
2025-06-20 14:29:54,212 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 25.38s - 5 prompts executed
2025-06-20 14:37:55,238 - main - INFO - Epinnox v6 starting up...
2025-06-20 14:37:55,255 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 14:37:55,255 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 14:37:55,256 - main - INFO - Performance monitoring initialized
2025-06-20 14:37:55,264 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 14:37:55,264 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 14:37:55,266 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 14:38:01,709 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 14:38:02,418 - websocket - INFO - Websocket connected
2025-06-20 14:38:05,093 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 14:38:05,093 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 14:38:05,094 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 14:38:05,096 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 14:38:05,101 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 14:38:07,152 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 14:38:07,153 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 14:38:07,153 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 14:38:07,154 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 14:38:07,155 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 14:38:07,158 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 14:38:07,159 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 14:38:07,168 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 14:38:07,170 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 14:38:07,171 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 14:38:07,178 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_143807_26ad7bee
2025-06-20 14:38:07,180 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_143807_26ad7bee
2025-06-20 14:38:07,236 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 14:40:30,526 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:40:30,526 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 14:40:30,526 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 14:40:30,528 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:40:34,068 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 14:40:34,069 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 14:40:34,069 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 14:40:34,072 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 14:40:34,072 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:40:34,072 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:40:34,073 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 14:40:34,073 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:40:38,551 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 645 chars
2025-06-20 14:40:38,551 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent upward trend in Bitcoin's price over the past three months, with an average monthly increase of approximately 8%. However, there is significant volatility and recent news about reg...
2025-06-20 14:40:38,552 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 167, Total: 289
2025-06-20 14:40:38,552 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT UPWARD TREND IN BITCOIN'S PRICE OVER THE PAST THREE MONTHS, WITH AN AVERAGE MONTHLY INCREASE OF APPROXIMATELY 8%. HOWEVER, THERE IS SIGNIFICANT VOLATILITY AND RECENT NEWS ABOUT REGULATORY CHANGES THAT COULD NEGATIVELY IMPACT INVESTOR SENTIMENT. GIVEN THESE FACTORS, I RECOMMEND TAKING A SHORT POSITION TO CAPITALIZE ON POTENTIAL DOWNWARD MOVEMENT WHILE MAINTAINING SOME RISK MITIGATION THROUGH THE TAKE-PROFIT AT 10% IF PRICES START TRENDING UPWARDS AGAIN OR REACH MY TARGET SELL PRICE BEFORE REACHING STOP LOSS LEVEL OF 20%.", 'ACTION': 'ENTER_NOW'}
2025-06-20 14:40:38,553 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-20 14:40:38,553 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:40:38,554 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:40:38,555 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162255/$0.162256
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161444
Resistance: $0.163066
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 14:40:38,555 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:40:44,421 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 855 chars
2025-06-20 14:40:44,422 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 14:40:44,423 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 245, Total: 770
2025-06-20 14:40:44,425 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF BREACHED. MOMENTUM REMAINS NEUTRAL AND SPREAD CONDITIONS ARE FAVORABLE FOR SHORTING DUE TO LOW VOLATILITY INDICATED BY L2 IMBALANCE AT 0.0%. THE RISK/REWARD RATIO OF 2:1 JUSTIFIES A MODERATE TAKE-PROFIT LEVEL, WHILE THE STOP LOSS IS SET HIGHER THAN USUAL GIVEN MARKET STABILITY BUT STILL WITHIN REASONABLE BOUNDS CONSIDERING POTENTIAL SUPPORT BREACH RISKS.', 'ACTION': 'ENTER_NOW'}
2025-06-20 14:40:44,426 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-20 14:40:44,428 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-20 14:40:44,428 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-20 14:40:44,429 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:40:44,429 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:40:44,430 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 14:40:44,431 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:40:49,864 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 693 chars
2025-06-20 14:40:49,865 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.20%
STOP_LOSS: -3.00%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes or increased volatility not yet reflected in the confidence level of unknown regimes. A short positi...
2025-06-20 14:40:49,865 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 215, Total: 782
2025-06-20 14:40:49,867 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES OR INCREASED VOLATILITY NOT YET REFLECTED IN THE CONFIDENCE LEVEL OF UNKNOWN REGIMES. A SHORT POSITION REDUCES EXPOSURE TO FURTHER LOSSES WHILE AWAITING CLEARER SIGNALS FOR ADJUSTMENT. INCREASED TAKE-PROFIT IS SET DUE TO IMPROVED ODDS FROM A RISK REDUCTION STRATEGY; HOWEVER, STOP LOSS REMAINS CONSERVATIVE GIVEN MARKET UNCERTAINTY AND RECENT PERFORMANCE TRENDS.', 'ACTION': 'ENTER_NOW'}
2025-06-20 14:40:49,868 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-20 14:40:49,869 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 14:40:49,869 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:40:49,870 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:40:49,871 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162255 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154142 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154142 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154142 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 14:40:49,871 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:40:54,815 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 736 chars
2025-06-20 14:40:54,815 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The analysis of the market data indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, which aligns well with our opportunity criteria including risk/reward ratio greater than 2:1....
2025-06-20 14:40:54,816 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 177, Total: 829
2025-06-20 14:40:54,816 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE ANALYSIS OF THE MARKET DATA INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, WHICH ALIGNS WELL WITH OUR OPPORTUNITY CRITERIA INCLUDING RISK/REWARD RATIO GREATER THAN 2:1. GIVEN THAT THERE IS SUFFICIENT ACCOUNT CAPACITY TO TAKE ON THIS POSITION WITHOUT EXCEEDING THE REMAINING RISK BUDGET OF 3%, A LONG TRADE AT CURRENT LEVELS SEEMS JUSTIFIED. THE CONFIDENCE LEVEL IN THIS DECISION STANDS HIGH DUE TO STRONG SETUP QUALITY AND FAVORABLE MARKET CONDITIONS, WITH AN EXPECTED TIME HORIZON FOR POTENTIAL PROFITABILITY WITHIN THE NEXT 5-15 MINUTES BASED ON HISTORICAL DATA PATTERNS ASSOCIATED WITH SIMILAR SETUPS.', 'ACTION': 'ENTER_NOW'}
2025-06-20 14:40:54,818 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-20 14:40:54,819 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 24.29s - 5 prompts executed
2025-06-20 15:01:32,610 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:01:32,611 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 15:01:32,611 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 15:01:32,612 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:01:36,005 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 15:01:36,006 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 15:01:36,006 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 15:01:36,007 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 15:01:36,007 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:01:36,008 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:01:36,008 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 15:01:36,009 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:01:40,213 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 659 chars
2025-06-20 15:01:40,213 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may begin to decline sharply, as indi...
2025-06-20 15:01:40,214 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 167, Total: 289
2025-06-20 15:01:40,215 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SUGGEST THAT WE ARE APPROACHING AN INFLECTION POINT WHERE PRICES MAY BEGIN TO DECLINE SHARPLY, AS INDICATED BY RECENT BEARISH CANDLESTICK FORMATIONS AND INCREASING VOLUME ON SELLING DAYS. GIVEN THESE INDICATORS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES PREMATURELY BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF FURTHER DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-20 15:01:40,215 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-20 15:01:40,216 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:01:40,216 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:01:40,217 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162954/$0.162963
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.162139
Resistance: $0.163769
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 15:01:40,217 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:01:45,342 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 900 chars
2025-06-20 15:01:45,342 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near resistance levels and has not yet confirmed through increased volume which could indicate an ...
2025-06-20 15:01:45,343 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 204, Total: 729
2025-06-20 15:01:45,343 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR RESISTANCE LEVELS AND HAS NOT YET CONFIRMED THROUGH INCREASED VOLUME WHICH COULD INDICATE AN IMPENDING REVERSAL OR CONSOLIDATION PERIOD AHEAD OF POTENTIAL DOWNWARD MOVEMENT DUE TO NEUTRAL TECHNICAL INDICATORS LIKE MOMENTUM AT 0%. THE FAVORABLE SPREAD CONDITIONS FURTHER SUPPORT A SHORT POSITION AS IT IMPLIES LOWER TRANSACTION COSTS FOR ENTERING. GIVEN THE RISK/REWARD RATIO IS SLIGHTLY ABOVE AVERAGE, AND CONSIDERING THAT THERE'S NO IMMEDIATE PRICE ACTION NEAR KEY LEVELS OR VOLUME CONFIRMATION YET (WHICH COULD SIGNAL AN UPTREND), TAKING ADVANTAGE OF POTENTIAL DOWNSIDE WITH STOP LOSS PROTECTION SEEMS PRUDENT WHILE MAINTAINING A MODERATE TAKE PROFIT TO CAPITALIZE ON ANY SHORT-TERM RALLIES.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:01:45,344 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-20 15:01:45,345 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-20 15:01:45,346 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-20 15:01:45,346 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:01:45,346 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:01:45,347 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 15:01:45,347 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:01:52,869 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1418 chars
2025-06-20 15:01:52,870 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes or increased volatility that the trader is not adequately prepared for with their existing risk manage...
2025-06-20 15:01:52,870 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 397, Total: 964
2025-06-20 15:01:52,872 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': "THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES OR INCREASED VOLATILITY THAT THE TRADER IS NOT ADEQUATELY PREPARED FOR WITH THEIR EXISTING RISK MANAGEMENT SETTINGS. BY REDUCING RISK ADJUSTMENT TO 0.5-2.0 (TAKING IT AT ITS LOWER END), WE CAN DECREASE EXPOSURE PER TRADE AND MITIGATE LOSSES IN A MORE UNCERTAIN MARKET REGIME, AS SUGGESTED BY THE DOWNWARD TREND OF WIN RATE AND NORMAL DRAWDOWN LEVEL WHICH DOESN'T INDICATE AN EXTREME RISK SCENARIO YET BUT REQUIRES CAUTION DUE TO POOR PERFORMANCE. THE TAKE PROFIT IS SET HIGHER THAN AVERAGE PROFIT (0.8%) CONSIDERING THAT WE ARE NOW FAVORING SHORT POSITIONS IN A POTENTIALLY BEARISH MARKET, AIMING FOR QUICKER EXITS BEFORE POTENTIAL LOSSES ESCALATE FURTHER. STOP LOSS HAS BEEN INCREASED FROM THE USUAL -0.3% DUE TO HEIGHTENED RISK AND RECENT POOR PERFORMANCE; THIS WILL HELP PROTECT AGAINST SIGNIFICANT DOWNSIDE WHILE STILL ALLOWING SOME ROOM FOR RECOVERY IF TRADES ARE EXECUTED NEAR ENTRY THRESHOLDS (70%). THE HOLD TIME TARGET REMAINS UNCHANGED AS IT IS WITHIN A MEDIUM SCALP SUITABILITY RANGE, WHICH SHOULD BE SUFFICIENT IN THE CURRENT MARKET REGIME.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:01:52,872 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-20 15:01:52,872 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 15:01:52,874 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:01:52,874 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:01:52,874 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162954 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154806 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154806 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154806 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 15:01:52,875 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:01:58,024 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 861 chars
2025-06-20 15:01:58,025 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 75%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.0%
EXPLANATION: The opportunity ranking for DOGE/USDT indicates a high setup quality with clear breakout patterns and volume confirmation, which aligns well with the momentum criteria of our analysis framework. Given that there is no sign...
2025-06-20 15:01:58,025 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 209, Total: 861
2025-06-20 15:01:58,026 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING FOR DOGE/USDT INDICATES A HIGH SETUP QUALITY WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, WHICH ALIGNS WELL WITH THE MOMENTUM CRITERIA OF OUR ANALYSIS FRAMEWORK. GIVEN THAT THERE IS NO SIGNIFICANT CHANGE IN MOM (MOVING AVERAGE), YET WE HAVE AN UPTREND CONFIRMED BY OTHER INDICATORS SUCH AS RSI OR MACD ABOVE THEIR RESPECTIVE THRESHOLDS, IT SUGGESTS A CONTINUATION RATHER THAN REVERSAL OF TRENDS DESPITE LACK OF IMMEDIATE PRICE MOVEMENT CHANGES. THE RISK/REWARD RATIO EXCEEDS OUR PREFERRED THRESHOLD AT 1:2.5 WITH THE TAKE-PROFIT SET TO CAPTURE GAINS WHILE LIMITING POTENTIAL LOSSES WITHIN REASONABLE BOUNDS CONSIDERING MARKET VOLATILITY AND ACCOUNT CAPACITY CONSTRAINTS, WHICH IS SUITABLE FOR A SCALPING STRATEGY IN THIS MODERATELY RISKY ENVIRONMENT.', 'ACTION': 'ENTER_NOW'}
2025-06-20 15:01:58,026 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-20 15:01:58,028 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 25.42s - 5 prompts executed
2025-06-20 15:02:24,208 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:02:24,209 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 15:02:24,210 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 15:02:24,211 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:02:27,585 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 15:02:27,586 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 15:02:27,586 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 15:02:27,587 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 15:02:27,588 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:02:27,588 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:02:27,589 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 15:02:27,589 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:02:31,879 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 698 chars
2025-06-20 15:02:31,879 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that this could be an inflated bubble simil...
2025-06-20 15:02:31,880 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 173, Total: 295
2025-06-20 15:02:31,881 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT THIS COULD BE AN INFLATED BUBBLE SIMILAR TO PREVIOUS ONES OBSERVED WITHIN THE CRYPTO MARKETS. A SHORT POSITION WOULD CAPITALIZE ON EXPECTED CORRECTIONS WHILE LIMITING RISK EXPOSURE THROUGH A TAKE-PROFIT AT 10% ABOVE CURRENT LEVELS, ENSURING GAINS BEFORE POTENTIAL MARKET REVERSAL AND SETTING STOP LOSS ORDERS AT 20% BELOW PRESENT PRICES AS AN ADDITIONAL SAFEGUARD AGAINST SIGNIFICANT LOSSES IN CASE OF UNEXPECTED UPWARD PRICE MOVEMENTS.', 'ACTION': 'ENTER_NOW'}
2025-06-20 15:02:31,881 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-20 15:02:31,882 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:02:31,882 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:02:31,883 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162957/$0.162963
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.162142
Resistance: $0.163772
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 15:02:31,883 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:02:37,220 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1097 chars
2025-06-20 15:02:37,220 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. Although the price is near key support levels and momentum remains neutral, there's an opportunity to capitalize on pote...
2025-06-20 15:02:37,221 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 237, Total: 762
2025-06-20 15:02:37,222 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. ALTHOUGH THE PRICE IS NEAR KEY SUPPORT LEVELS AND MOMENTUM REMAINS NEUTRAL, THERE'S AN OPPORTUNITY TO CAPITALIZE ON POTENTIAL DOWNWARD PRESSURE DUE TO L2 IMBALANCE BEING AT ZERO PERCENT WHICH COULD INDICATE LIQUIDITY ISSUES OR LACK OF INTEREST FROM LARGE MARKET PARTICIPANTS IN THIS PAIRING. THE FAVORABLE SPREAD CONDITIONS FURTHER ENHANCE ENTRY TIMING FOR A SHORT POSITION AS IT IMPLIES LOWER TRANSACTION COSTS AND BETTER EXECUTION PRICES, INCREASING THE LIKELIHOOD THAT ANY PRICE MOVEMENT WILL BE TOWARDS OUR TARGET STOP LOSS LEVEL BEFORE WE REACH TAKE PROFIT. GIVEN THESE FACTORS COMBINED WITH AN ACCEPTABLE RISK/REWARD RATIO OF 2:1, ENTERING NOW WOULD ALIGN WELL WITH MARKET CONDITIONS TO POTENTIALLY BENEFIT FROM A SHORT POSITION IN DOGE/USDT AT CURRENT LEVELS OR LOWER IF THE VOLUME CONFIRMATION INDICATES INCREASED LIQUIDITY AND TRADING ACTIVITY WHICH COULD DRIVE PRICES DOWN FURTHER TOWARDS OUR SUPPORT LEVEL.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:02:37,222 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-20 15:02:37,223 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-20 15:02:37,223 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-20 15:02:37,224 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:02:37,225 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:02:37,225 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162957 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154809 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154809 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154809 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 15:02:37,226 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:02:41,883 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 671 chars
2025-06-20 15:02:41,884 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget allows...
2025-06-20 15:02:41,884 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 172, Total: 824
2025-06-20 15:02:41,887 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, SUGGESTING STRONG BUYING MOMENTUM IN THE SHORT TERM (5-15 MINUTES). GIVEN THAT OUR RISK BUDGET ALLOWS US TO TAKE ON THIS POSITION WITHOUT EXCEEDING 3% OF AVAILABLE BALANCE, WE CAN CONFIDENTLY ENTER LONG POSITIONS. THE TAKE PROFIT IS SET AT A CONSERVATIVE LEVEL CONSIDERING MARKET VOLATILITY AND POTENTIAL SLIPPAGE WHILE ENSURING PROFITABILITY ABOVE THE PREFERRED >2:1 RATIO. A STOP LOSS WILL PROTECT AGAINST SUDDEN ADVERSE PRICE MOVEMENTS WITHIN OUR RISK TOLERANCE LEVELS.', 'ACTION': 'ENTER_NOW'}
2025-06-20 15:02:41,887 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-20 15:02:41,888 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 17.68s - 4 prompts executed
2025-06-20 15:02:54,187 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:02:54,187 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:02:54,187 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.163080/$0.163084
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.162265
Resistance: $0.163895
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 15:02:54,190 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:03:00,116 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1186 chars
2025-06-20 15:03:00,118 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, there is an L2 imbalance of zero percent which could indicate potential liquidity issues o...
2025-06-20 15:03:00,118 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 277, Total: 802
2025-06-20 15:03:00,119 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, THERE IS AN L2 IMBALANCE OF ZERO PERCENT WHICH COULD INDICATE POTENTIAL LIQUIDITY ISSUES OR OPPORTUNITIES FOR ARBITRAGE IF DETECTED BY SOPHISTICATED TRADERS. GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL AND MOMENTUM HAS NOT SHOWN A CLEAR DIRECTIONAL TREND, WE SHOULD CONSIDER SHORTING THE MARKET TO CAPITALIZE ON ANY POSSIBLE PRICE CORRECTION DUE TO THESE MICROSTRUCTURAL NUANCES. THE KEY LEVELS SUGGEST SUPPORT AT $0.162265 WITH RESISTANCE SLIGHTLY ABOVE IT; HOWEVER, SINCE THERE'S NO SIGNIFICANT VOLUME CONFIRMATION YET AND A FAVORABLE SPREAD CONDITION IS PRESENT (THOUGH MINIMAL), WE CAN TAKE ADVANTAGE OF THE CURRENT MARKET CONDITIONS BY INITIATING A SHORT POSITION NOW BUT SHOULD BE CAUTIOUS ABOUT POTENTIAL LIQUIDITY RISKS. THE RISK/REWARD RATIO AT 2:1 JUSTIFIES THIS DECISION, AS IT INDICATES THAT FOR EVERY DOLLAR LOST ON THE TRADE, THERE IS AN EXPECTATION TO GAIN TWO DOLLARS IN PROFIT IF OUR ANALYSIS HOLDS TRUE AND MARKET CONDITIONS REMAIN STABLE OR IMPROVE FURTHER.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:03:00,123 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-20 15:03:00,123 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-20 15:03:00,124 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-20 15:03:00,124 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 5.94s - 1 prompts executed
2025-06-20 15:03:09,204 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:03:09,205 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 15:03:09,205 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 15:03:09,206 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:03:12,702 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 117 chars
2025-06-20 15:03:12,702 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}...
2025-06-20 15:03:12,702 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 67, Total: 672
2025-06-20 15:03:12,704 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 15:03:12,705 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:03:12,705 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:03:12,706 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 15:03:12,706 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:03:17,680 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 953 chars
2025-06-20 15:03:17,680 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends show that similar patterns have led to significant ...
2025-06-20 15:03:17,681 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 218, Total: 340
2025-06-20 15:03:17,683 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SHORT TIMEFRAMES. GIVEN THESE INDICATORS, I RECOMMEND INITIATING A SHORT POSITION NOW AS IT PRESENTS AN OPPORTUNITY FOR PROFIT IF PRICES CONTINUE THEIR UPWARD TRAJECTORY BEFORE CORRECTING THEMSELVES. THE TAKE-PROFIT LEVEL IS SET AT 10% ABOVE THE ENTRY PRICE DUE TO EXPECTED VOLATILITY AND POTENTIAL SUPPORT LEVELS IDENTIFIED IN TECHNICAL ANALYSIS CHARTS. A STOP LOSS OF 20% BELOW THE ENTRY POINT WILL HELP MITIGATE RISKS IF MARKET CONDITIONS TURN UNFAVORABLE QUICKLY, ENSURING A SAFETY NET FOR CAPITAL PRESERVATION WHILE STILL ALLOWING ROOM FOR PROFITABILITY SHOULD PRICES STABILIZE OR INCREASE SLIGHTLY BEFORE DROPPING SIGNIFICANTLY.', 'ACTION': 'ENTER_NOW'}
2025-06-20 15:03:17,683 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-20 15:03:17,684 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:03:17,684 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:03:17,685 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.163119/$0.163127
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.162303
Resistance: $0.163935
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 15:03:17,686 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:03:22,755 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 843 chars
2025-06-20 15:03:22,756 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-20 15:03:22,756 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 202, Total: 727
2025-06-20 15:03:22,757 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF CONFIRMED BY INCREASED TRADING ACTIVITY (VOLUME CONFIRMATION). GIVEN THAT MOMENTUM AND ORDERFLOW ARE NEUTRAL BUT THERE'S AN ATTRACTIVE SPREAD CONDITION FAVORABLE FOR SHORT POSITIONS, A SHORT TRADE ON DOGE/USDT WITH HIGH CONFIDENCE SEEMS PRUDENT. THE TAKE-PROFIT IS SET AT 10% TO CAPITALIZE QUICKLY IF THE PRICE MOVES AGAINST OUR POSITION DUE TO POTENTIAL MARKET REACTIONS OR NEWS EVENTS THAT COULD DRIVE PRICES UP TEMPORARILY BEFORE CONTINUING DOWNWARD TOWARDS SUPPORT LEVELS, WHILE A STOP LOSS OF 20% PROVIDES PROTECTION IN CASE OF AN UNEXPECTED POSITIVE REVERSAL.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:03:22,758 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-20 15:03:22,758 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-20 15:03:22,759 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-20 15:03:22,760 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 13.56s - 3 prompts executed
2025-06-20 15:03:24,215 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 15:03:24,215 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 15:03:24,216 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.163135 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154978 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154978 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154978 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 15:03:24,216 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 15:03:29,004 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 642 chars
2025-06-20 15:03:29,004 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.0%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but has good liquidity as indicated by lo...
2025-06-20 15:03:29,005 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 175, Total: 827
2025-06-20 15:03:29,006 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE BEST OPPORTUNITY IS DOGE/USDT WITH A HIGH SETUP QUALITY INDICATING CLEAR BREAKOUT PATTERNS AND STRONG SIGNALS, WHICH ALIGNS WELL WITH THE CURRENT MARKET MOMENTUM THAT'S NEUTRAL BUT HAS GOOD LIQUIDITY AS INDICATED BY LOW SPREADS (<0.3%). GIVEN ITS 1-4% VOLATILITY SUITABLE FOR SCALPING STRATEGIES, IT FITS WITHIN OUR RISK/REWARD PREFERENCE OF >2:1 RATIO AND WE HAVE SUFFICIENT ACCOUNT CAPACITY TO TAKE A POSITION WITHOUT EXCEEDING THE REMAINING RISK BUDGET. THE TIME HORIZON IS SHORT AT 5-15 MINUTES WHICH ALLOWS US TO REACT QUICKLY IF MARKET CONDITIONS CHANGE.", 'ACTION': 'ENTER_NOW'}
2025-06-20 15:03:29,006 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-20 15:03:29,007 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 4.79s - 1 prompts executed
