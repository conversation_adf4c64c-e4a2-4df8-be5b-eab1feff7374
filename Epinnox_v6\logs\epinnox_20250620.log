2025-06-20 00:02:20,587 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:02:20,603 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:02:20,604 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:02:20,606 - main - INFO - Performance monitoring initialized
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:02:20,616 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:02:20,617 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:02:25,454 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:02:26,356 - websocket - INFO - Websocket connected
2025-06-20 00:02:28,640 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:02:28,641 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:02:28,641 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:02:28,642 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:02:28,646 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:02:30,734 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:02:30,735 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:02:30,736 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:02:30,739 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:02:30,742 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:02:30,743 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:02:30,754 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:02:30,755 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:02:30,755 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:02:30,761 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:02:30,765 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_000230_d0badc0c
2025-06-20 00:11:47,033 - main - INFO - Epinnox v6 starting up...
2025-06-20 00:11:47,047 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 00:11:47,048 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 00:11:47,049 - main - INFO - Performance monitoring initialized
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 00:11:47,056 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 00:11:47,058 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 00:11:52,335 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 00:11:53,254 - websocket - INFO - Websocket connected
2025-06-20 00:11:55,890 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 00:11:55,890 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 00:11:55,892 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 00:11:55,893 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 00:11:55,898 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 00:11:57,928 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,929 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 00:11:57,930 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 00:11:57,931 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 00:11:57,933 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 00:11:57,934 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 00:11:57,942 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 00:11:57,943 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 00:11:57,944 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 00:11:57,948 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 00:11:57,952 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_001157_27614757
2025-06-20 08:23:44,214 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:23:44,244 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:23:44,245 - main - INFO - Performance monitoring initialized
2025-06-20 08:23:44,258 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:23:44,259 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:23:44,260 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:23:55,818 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:23:56,752 - websocket - INFO - Websocket connected
2025-06-20 08:23:59,900 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:23:59,901 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:23:59,901 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:23:59,902 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:23:59,908 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:24:02,054 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,055 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:24:02,056 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:24:02,056 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:24:02,060 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:24:02,060 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:24:02,092 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:24:02,094 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:24:02,094 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:24:02,098 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,102 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082402_7e473ab6
2025-06-20 08:24:02,171 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 08:25:05,899 - main - INFO - Epinnox v6 starting up...
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 08:25:05,916 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 08:25:05,917 - main - INFO - Performance monitoring initialized
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 08:25:05,926 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 08:25:05,928 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 08:25:13,075 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 08:25:13,963 - websocket - INFO - Websocket connected
2025-06-20 08:25:16,800 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 08:25:16,805 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 08:25:16,805 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 08:25:16,806 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 08:25:16,811 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 08:25:18,882 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 08:25:18,884 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,885 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 08:25:18,887 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 08:25:18,887 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 08:25:18,890 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 08:25:18,890 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 08:25:18,898 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 08:25:18,901 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 08:25:18,902 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 08:25:18,909 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,912 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_082518_5ef06b15
2025-06-20 08:25:18,970 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 09:19:16,806 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-20 10:33:59,743 - main - INFO - Epinnox v6 starting up...
2025-06-20 10:33:59,804 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 10:33:59,805 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 10:33:59,809 - main - INFO - Performance monitoring initialized
2025-06-20 10:33:59,832 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 10:33:59,833 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 10:33:59,835 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 10:34:27,417 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:34:28,188 - websocket - INFO - Websocket connected
2025-06-20 10:34:30,405 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 10:34:30,407 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 10:34:30,409 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 10:34:30,420 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 10:34:30,441 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 10:34:32,561 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 10:34:32,563 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,566 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 10:34:32,570 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 10:34:32,574 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 10:34:32,582 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 10:34:32,582 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 10:34:32,610 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 10:34:32,630 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 10:34:32,631 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 10:34:32,641 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,653 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_103432_b7c30162
2025-06-20 10:34:32,756 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 10:48:59,366 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:05,490 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:05,490 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 53.08 (spread: 0.000%, atr: 0.025263, depth: 35335)
2025-06-20 10:49:05,514 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:05,521 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:18,620 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:24,367 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:24,368 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.84 (spread: 0.017%, atr: 0.000000, depth: 3019981)
2025-06-20 10:49:24,377 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:28,383 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:34,620 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:34,627 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.07 (spread: 0.017%, atr: 0.000000, depth: 5067876)
2025-06-20 10:49:34,643 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:38,379 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:43,884 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:43,893 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 52.76 (spread: 0.009%, atr: 0.000000, depth: 3295931)
2025-06-20 10:49:43,900 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:49,031 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:49:55,535 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:49:55,537 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.93 (spread: 0.009%, atr: 0.000000, depth: 7496739)
2025-06-20 10:49:55,562 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:49:58,396 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:04,285 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:04,285 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 59.11 (spread: 0.017%, atr: 0.000000, depth: 7144241)
2025-06-20 10:50:04,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:08,404 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:14,305 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:14,308 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 56.98 (spread: 0.026%, atr: 0.000000, depth: 6628940)
2025-06-20 10:50:14,318 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:18,617 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:24,232 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:24,236 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.34 (spread: 0.017%, atr: 0.000000, depth: 5308213)
2025-06-20 10:50:24,248 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:28,400 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:34,166 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:34,166 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.82 (spread: 0.009%, atr: 0.000000, depth: 11127428)
2025-06-20 10:50:34,182 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:38,401 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:44,101 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:44,101 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.56 (spread: 0.009%, atr: 0.000000, depth: 10954232)
2025-06-20 10:50:44,109 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:49,228 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:50:55,061 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:50:55,062 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.08 (spread: 0.026%, atr: 0.000000, depth: 6136076)
2025-06-20 10:50:55,078 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:50:58,407 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:04,591 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:04,592 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 53.30 (spread: 0.009%, atr: 0.000000, depth: 4797782)
2025-06-20 10:51:04,595 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:08,414 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:14,248 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:14,248 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.50 (spread: 0.009%, atr: 0.000000, depth: 7606578)
2025-06-20 10:51:14,272 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:18,665 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:24,824 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:24,824 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 48.47 (spread: 0.009%, atr: 0.000000, depth: 1565993)
2025-06-20 10:51:24,839 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:28,418 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:33,909 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:33,916 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 47.38 (spread: 0.009%, atr: 0.000000, depth: 1780332)
2025-06-20 10:51:33,933 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:38,411 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:44,150 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:44,150 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.26 (spread: 0.017%, atr: 0.000000, depth: 4143947)
2025-06-20 10:51:44,165 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:48,656 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:51:54,272 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:51:54,273 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.36 (spread: 0.017%, atr: 0.000000, depth: 4881429)
2025-06-20 10:51:54,289 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 10:51:58,416 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-06-20 10:52:03,980 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-06-20 10:52:03,980 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.79 (spread: 0.009%, atr: 0.000000, depth: 5076147)
2025-06-20 10:52:04,004 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 11:17:15,634 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-20 13:32:55,016 - main - INFO - Epinnox v6 starting up...
2025-06-20 13:32:55,034 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 13:32:55,034 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 13:32:55,034 - main - INFO - Performance monitoring initialized
2025-06-20 13:32:55,050 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 13:32:55,051 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 13:32:55,053 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 13:33:02,224 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:33:02,950 - websocket - INFO - Websocket connected
2025-06-20 13:33:04,652 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 13:33:04,653 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 13:33:04,653 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 13:33:04,654 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 13:33:04,659 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 13:33:06,788 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 13:33:06,788 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 13:33:06,789 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 13:33:06,789 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 13:33:06,789 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 13:33:06,794 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 13:33:06,795 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 13:33:06,814 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 13:33:06,817 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 13:33:06,817 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 13:33:06,825 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_133306_a3efe816
2025-06-20 13:33:06,827 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_133306_a3efe816
2025-06-20 13:33:06,892 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 13:34:07,203 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:34:10,707 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:35:14,213 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,214 - core.llm_orchestrator - ERROR - Error in risk assessment: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,216 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,221 - core.llm_orchestrator - ERROR - Error in strategy adaptation: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,223 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:14,223 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.01s - 5 prompts executed
2025-06-20 13:35:29,225 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:35:44,253 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:44,254 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:35:44,255 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 2 prompts executed
2025-06-20 13:35:52,848 - core.llm_orchestrator - ERROR - Error in emergency response: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,431 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,434 - core.llm_orchestrator - ERROR - Error in risk assessment: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,436 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,437 - core.llm_orchestrator - ERROR - Error in strategy adaptation: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,439 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:26,440 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.01s - 5 prompts executed
2025-06-20 13:37:41,412 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:37:56,820 - core.llm_orchestrator - ERROR - Error in market regime detection: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:56,822 - core.llm_orchestrator - ERROR - Error in entry timing: 'LMStudioRunner' object has no attribute 'generate_response'
2025-06-20 13:37:56,823 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 2 prompts executed
2025-06-20 13:41:44,521 - main - INFO - Epinnox v6 starting up...
2025-06-20 13:41:44,537 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 13:41:44,537 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 13:41:44,538 - main - INFO - Performance monitoring initialized
2025-06-20 13:41:44,547 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 13:41:44,547 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 13:41:44,548 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 13:41:50,447 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 13:41:51,166 - websocket - INFO - Websocket connected
2025-06-20 13:41:53,064 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 13:41:53,065 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 13:41:53,065 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 13:41:53,066 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 13:41:53,072 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 13:41:55,136 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 13:41:55,136 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 13:41:55,137 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 13:41:55,139 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 13:41:55,139 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 13:41:55,143 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 13:41:55,144 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 13:41:55,154 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 13:41:55,155 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 13:41:55,155 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 13:41:55,161 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_134155_f43dbd55
2025-06-20 13:41:55,164 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_134155_f43dbd55
2025-06-20 13:41:55,223 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 13:42:35,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:35,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:42:35,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:42:35,166 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:50,604 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:42:50,606 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:42:50,606 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:42:50,607 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:42:50,607 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:50,608 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:50,608 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:42:50,609 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:54,448 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 465 chars
2025-06-20 13:42:54,449 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a bullish trend with increasing volume and rising prices. However, the recent spike in price is accompanied by an increase in volatility which suggests potential overbought conditions that ...
2025-06-20 13:42:54,449 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 123, Total: 245
2025-06-20 13:42:54,450 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:54,452 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:54,452 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162586/$0.162592
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161773
Resistance: $0.163399
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:42:54,453 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:42:59,487 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 828 chars
2025-06-20 13:42:59,488 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:42:59,488 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 197, Total: 722
2025-06-20 13:42:59,489 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:42:59,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:42:59,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:42:59,491 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:05,752 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 847 chars
2025-06-20 13:43:05,753 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, suggesting the need for adjustment despite normal market volatility levels. A risk reduction of half will decrease potential losses while maintai...
2025-06-20 13:43:05,754 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 276, Total: 843
2025-06-20 13:43:05,757 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 0.5x
2025-06-20 13:43:05,757 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 0.5x, Hold time 8min
2025-06-20 13:43:05,758 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:05,759 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:05,759 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162586 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:43:05,760 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:11,620 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 945 chars
2025-06-20 13:43:11,621 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum being neutral but not trending strongly in any direction t...
2025-06-20 13:43:11,621 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 242, Total: 894
2025-06-20 13:43:11,623 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 36.46s - 5 prompts executed
2025-06-20 13:43:20,408 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:20,408 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:43:20,409 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:43:20,410 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:43:23,957 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:43:23,958 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:43:23,959 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:23,959 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:23,960 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:43:23,960 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:29,031 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 911 chars
2025-06-20 13:43:29,031 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-20 13:43:29,032 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 212, Total: 334
2025-06-20 13:43:29,033 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:29,033 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:29,035 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162524/$0.162525
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161711
Resistance: $0.163337
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:43:29,036 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:35,220 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1202 chars
2025-06-20 13:43:35,221 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, there is an upcoming resistance level at $0.163337 which could be challenging for bullish ...
2025-06-20 13:43:35,221 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 278, Total: 803
2025-06-20 13:43:35,222 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162524 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154398 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:43:35,223 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:40,033 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 649 chars
2025-06-20 13:43:40,034 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking JSON indicates a high-confidence setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budge...
2025-06-20 13:43:40,034 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 171, Total: 823
2025-06-20 13:43:40,034 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 19.63s - 4 prompts executed
2025-06-20 13:43:50,169 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:43:50,169 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:43:50,170 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162530/$0.162531
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161717
Resistance: $0.163343
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:43:50,172 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:43:55,211 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 716 chars
2025-06-20 13:43:55,212 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:43:55,212 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 183, Total: 708
2025-06-20 13:43:55,213 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 5.05s - 1 prompts executed
2025-06-20 13:44:05,400 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:05,401 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:44:05,402 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:44:05,402 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:44:09,107 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:44:09,109 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:44:09,109 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:09,109 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:09,110 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:44:09,110 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:13,996 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 877 chars
2025-06-20 13:44:13,997 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within three months, suggesting an immine...
2025-06-20 13:44:13,997 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 202, Total: 324
2025-06-20 13:44:13,998 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:13,999 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:13,999 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162541/$0.162542
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161728
Resistance: $0.163354
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:44:14,000 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:19,182 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 874 chars
2025-06-20 13:44:19,183 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-20 13:44:19,183 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 204, Total: 729
2025-06-20 13:44:19,187 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 13.79s - 3 prompts executed
2025-06-20 13:44:35,169 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:35,170 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:35,170 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162586/$0.162587
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161773
Resistance: $0.163399
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:44:35,171 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:40,934 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1071 chars
2025-06-20 13:44:40,936 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-20 13:44:40,936 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 238, Total: 763
2025-06-20 13:44:40,937 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:44:40,938 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:45,941 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 778 chars
2025-06-20 13:44:45,942 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy is showing a declining win rate and poor recent performance, which indicates that the market regime might be changing. Given this uncertainty (Regime Confidence at 50%) combined with normal volatility ...
2025-06-20 13:44:45,942 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 188, Total: 755
2025-06-20 13:44:45,943 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 13:44:45,944 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:44:45,944 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:44:45,946 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162586 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154457 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:44:45,947 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:44:50,808 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 615 chars
2025-06-20 13:44:50,808 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget allows...
2025-06-20 13:44:50,809 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 172, Total: 824
2025-06-20 13:44:50,810 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 15.64s - 3 prompts executed
2025-06-20 13:45:05,538 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:05,539 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:45:05,539 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:45:05,541 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:09,000 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 117 chars
2025-06-20 13:45:09,001 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}...
2025-06-20 13:45:09,001 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 67, Total: 672
2025-06-20 13:45:09,002 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:45:09,002 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:09,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:09,003 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:45:09,004 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:13,619 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 686 chars
2025-06-20 13:45:13,620 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent downward trend in Bitcoin's price over the past week, with volume increasing as sellers step up. Technical indicators such as RSI and Moving Averages are signaling an oversold cond...
2025-06-20 13:45:13,620 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 180, Total: 302
2025-06-20 13:45:13,621 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:13,622 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:13,622 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162543/$0.162544
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161730
Resistance: $0.163356
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:13,623 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:19,642 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 940 chars
2025-06-20 13:45:19,642 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:45:19,643 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 259, Total: 784
2025-06-20 13:45:19,644 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 1 column 179 (char 178)
2025-06-20 13:45:19,645 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 14.11s - 3 prompts executed
2025-06-20 13:45:35,146 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:35,147 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:35,147 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162702/$0.162703
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161888
Resistance: $0.163516
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:35,149 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:40,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 821 chars
2025-06-20 13:45:40,188 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:45:40,190 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 188, Total: 713
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:40,191 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162702 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154567 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:45:40,192 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:45,189 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 702 chars
2025-06-20 13:45:45,190 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The best opportunity according to the JSON data is DOGE/USDT, which has a high setup quality with clear breakout patterns and volume confirmation within an optimal time horizon of 5-15 minutes. Given its alignment with tre...
2025-06-20 13:45:45,191 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 184, Total: 836
2025-06-20 13:45:45,192 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.05s - 2 prompts executed
2025-06-20 13:45:50,488 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:45:50,490 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:54,064 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:45:54,065 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:45:54,065 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:45:54,066 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:45:54,067 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:54,068 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:54,068 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:45:54,069 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:45:57,833 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 457 chars
2025-06-20 13:45:57,834 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volume but low price stability. Historical trends show that similar patterns have led to significant correctio...
2025-06-20 13:45:57,834 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 119, Total: 241
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:45:57,836 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162663/$0.162664
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161850
Resistance: $0.163476
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:45:57,837 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:04,616 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1178 chars
2025-06-20 13:46:04,616 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-20 13:46:04,617 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 311, Total: 836
2025-06-20 13:46:04,619 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-06-20 13:46:04,620 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 14.13s - 3 prompts executed
2025-06-20 13:46:20,552 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:20,554 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:46:20,554 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:46:20,556 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:24,074 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:46:24,074 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:46:24,076 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:46:24,077 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:46:24,078 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:24,079 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:24,080 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162664/$0.162665
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161851
Resistance: $0.163477
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:46:24,081 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:30,483 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1034 chars
2025-06-20 13:46:30,483 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price range. Although there is not an immediate signal from technical analysis or ML ensemble indicating s...
2025-06-20 13:46:30,484 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 275, Total: 800
2025-06-20 13:46:30,485 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-06-20 13:46:30,486 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 9.93s - 2 prompts executed
2025-06-20 13:46:35,226 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:35,227 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:35,227 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 13:46:35,229 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:40,299 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 805 chars
2025-06-20 13:46:40,300 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 70%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy has a win rate trending downwards, which indicates that the market regime might be shifting. Although there's no significant increase in volatility or drawdown level and recent performance is poor but ...
2025-06-20 13:46:40,300 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 194, Total: 761
2025-06-20 13:46:40,301 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 13:46:40,301 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162631 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154499 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:46:40,302 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:45,236 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 653 chars
2025-06-20 13:46:45,237 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but stable enough for scalping opportunitie...
2025-06-20 13:46:45,237 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 173, Total: 825
2025-06-20 13:46:45,239 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.01s - 2 prompts executed
2025-06-20 13:46:50,192 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:50,193 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:50,193 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:46:50,194 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:46:54,550 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 485 chars
2025-06-20 13:46:54,550 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within two weeks, suggesting an imminent ...
2025-06-20 13:46:54,552 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-20 13:46:54,553 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:46:54,553 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:46:54,554 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162649/$0.162664
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161836
Resistance: $0.163462
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:46:54,554 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:00,639 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 954 chars
2025-06-20 13:47:00,639 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no significant volume spikes, suggesting stability. Although the price is near key support levels and momentum remains neutral, there's an opportunity to capit...
2025-06-20 13:47:00,640 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 266, Total: 791
2025-06-20 13:47:00,641 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 1 column 192 (char 191)
2025-06-20 13:47:00,642 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 10.45s - 2 prompts executed
2025-06-20 13:47:05,179 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:05,180 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:47:05,181 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:47:05,182 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:08,674 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:47:08,674 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:47:08,675 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:47:08,676 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:47:08,676 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 3.50s - 1 prompts executed
2025-06-20 13:47:20,140 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:20,141 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:20,142 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162736/$0.162737
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161922
Resistance: $0.163550
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:47:20,144 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:25,260 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 794 chars
2025-06-20 13:47:25,261 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 65%
TAKE_PROFIT: N/A (as we are waiting for a market entry)
STOP_LOSS: N/A (same reason as take profit percentage is not applicable here)
EXPLANATION: The current signal analysis indicates that the technical indicators and volume flow data do not strongly suggest an immedi...
2025-06-20 13:47:25,262 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 189, Total: 714
2025-06-20 13:47:25,263 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 5.12s - 1 prompts executed
2025-06-20 13:47:35,176 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 0.00s - 0 prompts executed
2025-06-20 13:47:50,139 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:50,140 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 13:47:50,141 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 13:47:50,142 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:53,712 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 13:47:53,712 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 13:47:53,713 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 13:47:53,714 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 13:47:53,714 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:53,715 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:53,716 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 13:47:53,716 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:47:58,851 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 921 chars
2025-06-20 13:47:58,852 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may begin to decline, as indicated by...
2025-06-20 13:47:58,852 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 216, Total: 338
2025-06-20 13:47:58,853 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:47:58,853 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:47:58,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162668/$0.162669
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161855
Resistance: $0.163481
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 13:47:58,854 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:03,707 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 736 chars
2025-06-20 13:48:03,708 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-20 13:48:03,708 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 171, Total: 696
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:48:03,710 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162668 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154535 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 13:48:03,712 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:08,447 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 611 chars
2025-06-20 13:48:08,447 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but stable enough for scalping opportunitie...
2025-06-20 13:48:08,449 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 158, Total: 810
2025-06-20 13:48:08,450 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 18.31s - 4 prompts executed
2025-06-20 13:48:09,173 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 13:48:09,174 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 13:48:09,174 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🚨 EMERGENCY RESPONSE SYSTEM - IMMEDIATE ACTION REQUIRED

⚠️ EMERGENCY FLAGS: MANUAL_EMERGENCY_STOP
💰 ACCOUNT: $50.00 | RISK: $0.00
📊 POSITIONS (0):
• No open positions

📈 MARKET CONDITIONS:
Price: $0.162669 | Spread: 0.000%
Volume: NORMAL | Volatility: NORMAL

🎯 EMERGENCY PROTOCOLS:
- CLOSE_ALL: Close all positions immediately (market orders)
- CLOSE_LOSING: Close only losing positions
- HEDGE: Open hedge positions to reduce risk
- MONITOR: Continue monitoring, no immediate action
- REDUCE_SIZE:...
2025-06-20 13:48:09,175 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 13:48:13,359 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 460 chars
2025-06-20 13:48:13,360 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: MONITOR
CONFIDENCE: 85%
TAKE_PROFIT: N/A (No positions to profit from)
STOP_LOSS: N/A (No open positions at risk of loss)
EXPLANATION: The market conditions are stable with normal price and volume, indicating no immediate threat. There are currently no active positions which eliminates the...
2025-06-20 13:48:13,361 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 469, Completion: 123, Total: 592
2025-06-20 13:48:13,362 - core.llm_response_parsers - WARNING - No JSON found in emergency response, using fallback
2025-06-20 14:02:04,157 - main - INFO - Epinnox v6 starting up...
2025-06-20 14:02:04,173 - core.performance_monitor - INFO - Performance monitoring started
2025-06-20 14:02:04,174 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-20 14:02:04,174 - main - INFO - Performance monitoring initialized
2025-06-20 14:02:04,181 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-20 14:02:04,182 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-20 14:02:04,183 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-20 14:02:10,246 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-20 14:02:10,962 - websocket - INFO - Websocket connected
2025-06-20 14:02:12,732 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-20 14:02:12,732 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-20 14:02:12,733 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-20 14:02:12,735 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-20 14:02:12,741 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-20 14:02:14,796 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-20 14:02:14,797 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-20 14:02:14,797 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-20 14:02:14,798 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-20 14:02:14,799 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-20 14:02:14,801 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-20 14:02:14,802 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-20 14:02:14,810 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-20 14:02:14,811 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-20 14:02:14,812 - storage.session_manager - INFO - Session Manager initialized
2025-06-20 14:02:14,821 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250620_140214_fd9b433a
2025-06-20 14:02:14,822 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250620_140214_fd9b433a
2025-06-20 14:02:14,882 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-20 14:29:28,836 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:28,836 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-20 14:29:28,837 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-20 14:29:28,837 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:32,508 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-20 14:29:32,508 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-20 14:29:32,509 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-20 14:29:32,510 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-20 14:29:32,510 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-20 14:29:32,513 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:37,382 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 801 chars
2025-06-20 14:29:37,383 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with its price trending upwards despite lackluster fundamentals and increasing regulatory scrutiny. Historical volatility is hig...
2025-06-20 14:29:37,384 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 192, Total: 314
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:37,387 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.161780/$0.161781
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.160971
Resistance: $0.162589
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-20 14:29:37,388 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:42,668 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 885 chars
2025-06-20 14:29:42,669 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, despite this balance and favorable spread conditions, there is still neutrality from techn...
2025-06-20 14:29:42,670 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 210, Total: 735
2025-06-20 14:29:42,673 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:42,673 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:42,674 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-20 14:29:42,675 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:48,895 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1084 chars
2025-06-20 14:29:48,896 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy has a win rate trending downwards, which is not ideal. Although the drawdown level and market volatility are normal, recent performance indicates poor results with an average loss of -0.30%. To mitigat...
2025-06-20 14:29:48,896 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 263, Total: 830
2025-06-20 14:29:48,897 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-20 14:29:48,899 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-20 14:29:48,899 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-20 14:29:48,900 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.161780 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.153691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-20 14:29:48,900 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-20 14:29:54,210 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 793 chars
2025-06-20 14:29:54,210 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-confidence setup for DOGE/USDT with an identified breakout pattern and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk bud...
2025-06-20 14:29:54,211 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 196, Total: 848
2025-06-20 14:29:54,212 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 25.38s - 5 prompts executed
