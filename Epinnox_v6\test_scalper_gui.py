#!/usr/bin/env python3
"""
Test script for ScalperGPT GUI integration
Tests the new GUI components and data display functionality
"""

import sys
import os
import json

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scalper_gui_components():
    """Test ScalperGPT GUI component creation and data handling"""
    print("🧪 Testing ScalperGPT GUI Components...")
    
    # Test market data structure
    test_market_data = {
        'best_bid': 0.17234,
        'best_ask': 0.17236,
        'spread': 0.00002,
        'spread_pct': 0.012,
        'top_5_bids': [
            (0.17234, 1500.0),
            (0.17233, 2200.0),
            (0.17232, 1800.0),
            (0.17231, 3000.0),
            (0.17230, 2500.0)
        ],
        'top_5_asks': [
            (0.17236, 1600.0),
            (0.17237, 2100.0),
            (0.17238, 1900.0),
            (0.17239, 2800.0),
            (0.17240, 2400.0)
        ],
        'tick_atr': 0.00015,
        'trade_flow_imbalance': 12.5,
        'volume_momentum': 8.3,
        'data_latency_ms': 45.0
    }
    
    # Test ensemble analysis structure
    test_ensemble_analysis = {
        'individual_models': "SVM: LONG (75.2%) [Weight: 1.20]\nRandom Forest: LONG (68.9%) [Weight: 1.10]",
        'majority_vote': 'LONG',
        'avg_confidence': 72.1,
        'confidence_std': 8.5,
        'weighted_score': 0.65,
        'consensus_strength': 62.5,
        'performance_weighted_decision': 'LONG'
    }
    
    # Test trade instruction structure
    test_trade_instruction = {
        'ACTION': 'BUY',
        'QUANTITY': 150.0,
        'LEVERAGE': 20,
        'STOP_LOSS': 0.0235,
        'TAKE_PROFIT': 0.0250,
        'RISK_PCT': 2.0,
        'ORDER_TYPE': 'MARKET',
        'confidence': 85.0,
        'entry_price': 0.17235
    }
    
    # Test data validation
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Market data completeness
    total_tests += 1
    required_market_fields = [
        'best_bid', 'best_ask', 'spread', 'spread_pct',
        'top_5_bids', 'top_5_asks', 'tick_atr',
        'trade_flow_imbalance', 'volume_momentum', 'data_latency_ms'
    ]
    
    if all(field in test_market_data for field in required_market_fields):
        print("✅ Market data structure: PASSED")
        tests_passed += 1
    else:
        print("❌ Market data structure: FAILED")
    
    # Test 2: Ensemble analysis completeness
    total_tests += 1
    required_ensemble_fields = [
        'individual_models', 'majority_vote', 'avg_confidence',
        'confidence_std', 'weighted_score', 'consensus_strength',
        'performance_weighted_decision'
    ]
    
    if all(field in test_ensemble_analysis for field in required_ensemble_fields):
        print("✅ Ensemble analysis structure: PASSED")
        tests_passed += 1
    else:
        print("❌ Ensemble analysis structure: FAILED")
    
    # Test 3: Trade instruction completeness
    total_tests += 1
    required_instruction_fields = [
        'ACTION', 'QUANTITY', 'LEVERAGE', 'RISK_PCT', 'ORDER_TYPE'
    ]
    
    if all(field in test_trade_instruction for field in required_instruction_fields):
        print("✅ Trade instruction structure: PASSED")
        tests_passed += 1
    else:
        print("❌ Trade instruction structure: FAILED")
    
    # Test 4: Order book data format
    total_tests += 1
    bids_valid = all(isinstance(bid, tuple) and len(bid) == 2 for bid in test_market_data['top_5_bids'])
    asks_valid = all(isinstance(ask, tuple) and len(ask) == 2 for ask in test_market_data['top_5_asks'])
    
    if bids_valid and asks_valid:
        print("✅ Order book data format: PASSED")
        tests_passed += 1
    else:
        print("❌ Order book data format: FAILED")
    
    # Test 5: Value ranges validation
    total_tests += 1
    spread_valid = 0 <= test_market_data['spread_pct'] <= 100
    confidence_valid = 0 <= test_ensemble_analysis['avg_confidence'] <= 100
    risk_valid = 0.5 <= test_trade_instruction['RISK_PCT'] <= 5.0
    leverage_valid = 1 <= test_trade_instruction['LEVERAGE'] <= 200
    
    if spread_valid and confidence_valid and risk_valid and leverage_valid:
        print("✅ Value ranges validation: PASSED")
        tests_passed += 1
    else:
        print("❌ Value ranges validation: FAILED")
    
    print(f"\n📊 GUI Component Tests: {tests_passed}/{total_tests} passed")
    return tests_passed == total_tests

def test_historical_verdicts_format():
    """Test historical verdicts data format for ScalperGPT"""
    print("\n🧪 Testing Historical Verdicts Format...")
    
    # Test ScalperGPT verdict entry
    test_verdict_entry = {
        'id': 'abc12345',
        'timestamp': '2024-01-15 14:30:25',
        'symbol': 'DOGE/USDT:USDT',
        'action': 'BUY',
        'quantity': 150.0,
        'leverage': '20x',
        'risk_pct': 2.0,
        'order_type': 'MARKET',
        'entry_price': 0.17235,
        'exit_price': None,
        'pnl_percentage': 0.0,
        'status': 'PENDING',
        'reasoning': 'ScalperGPT: BUY 150.0 @ 20x, 2.0% risk'
    }
    
    # Test data validation
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Required fields present
    total_tests += 1
    required_fields = [
        'id', 'timestamp', 'symbol', 'action', 'quantity',
        'leverage', 'risk_pct', 'order_type', 'entry_price',
        'status', 'reasoning'
    ]
    
    if all(field in test_verdict_entry for field in required_fields):
        print("✅ Verdict entry structure: PASSED")
        tests_passed += 1
    else:
        print("❌ Verdict entry structure: FAILED")
    
    # Test 2: Action values validation
    total_tests += 1
    valid_actions = ['BUY', 'SELL', 'WAIT']
    
    if test_verdict_entry['action'] in valid_actions:
        print("✅ Action values validation: PASSED")
        tests_passed += 1
    else:
        print("❌ Action values validation: FAILED")
    
    # Test 3: Numeric values validation
    total_tests += 1
    quantity_valid = isinstance(test_verdict_entry['quantity'], (int, float)) and test_verdict_entry['quantity'] >= 0
    risk_valid = isinstance(test_verdict_entry['risk_pct'], (int, float)) and 0.5 <= test_verdict_entry['risk_pct'] <= 5.0
    price_valid = isinstance(test_verdict_entry['entry_price'], (int, float)) and test_verdict_entry['entry_price'] > 0
    
    if quantity_valid and risk_valid and price_valid:
        print("✅ Numeric values validation: PASSED")
        tests_passed += 1
    else:
        print("❌ Numeric values validation: FAILED")
    
    print(f"\n📊 Historical Verdicts Tests: {tests_passed}/{total_tests} passed")
    return tests_passed == total_tests

def test_gui_update_functions():
    """Test GUI update function logic"""
    print("\n🧪 Testing GUI Update Functions...")
    
    # Mock GUI update scenarios
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Color coding logic for trade flow
    total_tests += 1
    def get_flow_color(flow_value):
        if flow_value > 0:
            return "GREEN"
        elif flow_value < 0:
            return "RED"
        else:
            return "YELLOW"
    
    flow_tests = [
        (12.5, "GREEN"),
        (-8.3, "RED"),
        (0.0, "YELLOW")
    ]
    
    flow_test_passed = all(get_flow_color(flow) == expected for flow, expected in flow_tests)
    
    if flow_test_passed:
        print("✅ Trade flow color coding: PASSED")
        tests_passed += 1
    else:
        print("❌ Trade flow color coding: FAILED")
    
    # Test 2: Risk level color coding
    total_tests += 1
    def get_risk_color(risk_pct):
        if risk_pct > 3.0:
            return "RED"
        elif risk_pct > 1.5:
            return "YELLOW"
        else:
            return "GREEN"
    
    risk_tests = [
        (4.0, "RED"),
        (2.5, "YELLOW"),
        (1.0, "GREEN")
    ]
    
    risk_test_passed = all(get_risk_color(risk) == expected for risk, expected in risk_tests)
    
    if risk_test_passed:
        print("✅ Risk level color coding: PASSED")
        tests_passed += 1
    else:
        print("❌ Risk level color coding: FAILED")
    
    # Test 3: Latency color coding
    total_tests += 1
    def get_latency_color(latency_ms):
        if latency_ms < 100:
            return "GREEN"
        elif latency_ms < 200:
            return "YELLOW"
        else:
            return "RED"
    
    latency_tests = [
        (50, "GREEN"),
        (150, "YELLOW"),
        (250, "RED")
    ]
    
    latency_test_passed = all(get_latency_color(latency) == expected for latency, expected in latency_tests)
    
    if latency_test_passed:
        print("✅ Latency color coding: PASSED")
        tests_passed += 1
    else:
        print("❌ Latency color coding: FAILED")
    
    print(f"\n📊 GUI Update Function Tests: {tests_passed}/{total_tests} passed")
    return tests_passed == total_tests

def main():
    """Run all ScalperGPT GUI tests"""
    print("🤖 SCALPER GPT GUI INTEGRATION TESTS")
    print("=" * 50)
    
    tests = [
        test_scalper_gui_components,
        test_historical_verdicts_format,
        test_gui_update_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 OVERALL GUI TESTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL GUI TESTS PASSED! ScalperGPT interface is ready.")
        return True
    else:
        print("⚠️ Some GUI tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
