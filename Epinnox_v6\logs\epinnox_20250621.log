2025-06-21 07:41:07,746 - main - INFO - Epinnox v6 starting up...
2025-06-21 07:41:07,793 - core.performance_monitor - INFO - Performance monitoring started
2025-06-21 07:41:07,794 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-21 07:41:07,795 - main - INFO - Performance monitoring initialized
2025-06-21 07:41:07,808 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-21 07:41:07,808 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-21 07:41:07,812 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-21 07:41:18,514 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-21 07:41:19,810 - websocket - INFO - Websocket connected
2025-06-21 07:41:22,535 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-21 07:41:22,535 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-21 07:41:22,536 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-21 07:41:22,537 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-21 07:41:22,545 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-21 07:41:24,599 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-21 07:41:24,600 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-21 07:41:24,601 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-21 07:41:24,604 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-21 07:41:24,605 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-21 07:41:24,608 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-21 07:41:24,609 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-21 07:41:24,630 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-21 07:41:24,631 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-21 07:41:24,632 - storage.session_manager - INFO - Session Manager initialized
2025-06-21 07:41:24,638 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250621_074124_b48065a8
2025-06-21 07:41:24,641 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250621_074124_b48065a8
2025-06-21 07:41:24,728 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-21 07:43:01,519 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:43:01,520 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 07:43:01,520 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 07:43:01,521 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:43:04,984 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 07:43:04,985 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 07:43:04,985 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 07:43:04,988 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 07:43:04,989 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:43:04,990 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:43:04,990 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 07:43:04,991 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:43:09,626 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 758 chars
2025-06-21 07:43:09,627 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns show that similar valuations have led to significant corrections within six months, suggesting an impe...
2025-06-21 07:43:09,627 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 188, Total: 310
2025-06-21 07:43:09,628 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SHOW THAT SIMILAR VALUATIONS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. VOLUME TRENDS ARE ALSO DECLINING AS INVESTOR INTEREST WANES, WHICH TYPICALLY PRECEDES SELL-OFFS. GIVEN THESE INDICATORS AND A MODERATE CONFIDENCE LEVEL DUE TO POTENTIAL EXTERNAL MARKET INFLUENCES NOT ACCOUNTED FOR IN THE DATA ANALYSIS, I RECOMMEND INITIATING A SHORT POSITION WITH A TAKE PROFIT AT 10% ABOVE CURRENT LEVELS (TO CAPITALIZE ON ANY TEMPORARY PRICE RESILIENCE) BUT SETTING STOP LOSSES AT 20% BELOW PRESENT PRICES TO MITIGATE RISK.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:43:09,629 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 07:43:09,630 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:43:09,630 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:43:09,630 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162630/$0.162650
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161817
Resistance: $0.163443
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 07:43:09,631 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:43:14,631 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 828 chars
2025-06-21 07:43:14,631 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, technical analysis shows momentum at zero and key levels are not near yet; this could impl...
2025-06-21 07:43:14,632 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 199, Total: 724
2025-06-21 07:43:14,634 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, TECHNICAL ANALYSIS SHOWS MOMENTUM AT ZERO AND KEY LEVELS ARE NOT NEAR YET; THIS COULD IMPLY POTENTIAL DOWNWARD PRESSURE ON PRICES DUE TO LACK OF SIGNIFICANT BUYING INTEREST OR UPWARD TREND CONTINUATION RESISTANCE BEING TESTED WITHOUT CLEAR SUPPORT BREACH. THE FAVORABLE SPREAD CONDITIONS INDICATE A COST-EFFECTIVE ENTRY POINT FOR SHORTING THE PAIR, WITH AN ACCEPTABLE RISK/REWARD RATIO THAT JUSTIFIES TAKING ACTION NOW RATHER THAN WAITING FURTHER (AS VOLUME CONFIRMATION IS STILL PENDING). A TAKE PROFIT OF 10% AND STOP LOSS AT 20% ARE SET TO MANAGE POTENTIAL VOLATILITY WHILE CAPITALIZING ON A POSSIBLE PRICE DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:43:14,635 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 07:43:14,636 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 07:43:14,636 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 07:43:14,637 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:43:14,638 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:43:14,638 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 07:43:14,639 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:43:19,355 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 713 chars
2025-06-21 07:43:19,355 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 15%
STOP_LOSS: -20%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating that the market regime may be shifting. Despite maintaining confidence in this approach with an adjusted risk multiplier of 0.5 (reducin...
2025-06-21 07:43:19,355 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 175, Total: 742
2025-06-21 07:43:19,358 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 60.0, 'EXPLANATION': "THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING THAT THE MARKET REGIME MAY BE SHIFTING. DESPITE MAINTAINING CONFIDENCE IN THIS APPROACH WITH AN ADJUSTED RISK MULTIPLIER OF 0.5 (REDUCING EXPOSURE), IT'S PRUDENT TO SHORT DUE TO POTENTIAL DOWNWARD TRENDS SUGGESTED BY OTHER ADAPTATION FACTORS LIKE NORMAL DRAWDOWN LEVELS BUT DECREASING WIN RATE AND RECENT PERFORMANCE METRICS. THE TAKE-PROFIT IS SET AT A MODERATE LEVEL CONSIDERING THE STRATEGY HAS BEEN PERFORMING WELL IN TERMS OF ROI, WHILE STOP LOSS ENSURES PROTECTION AGAINST FURTHER DECLINES GIVEN MARKET VOLATILITY REMAINS WITHIN NORMAL RANGES.", 'ACTION': 'ENTER_NOW'}
2025-06-21 07:43:19,358 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 07:43:19,358 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 07:43:19,359 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:43:19,359 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:43:19,360 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162630 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154498 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154498 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154498 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 07:43:19,361 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:43:23,970 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 650 chars
2025-06-21 07:43:23,971 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget allows...
2025-06-21 07:43:23,971 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 170, Total: 822
2025-06-21 07:43:23,973 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, SUGGESTING STRONG BUYING MOMENTUM IN THE SHORT TERM (5-15 MINUTES). GIVEN THAT OUR RISK BUDGET ALLOWS US TO TAKE ON THIS POSITION WITHOUT EXCEEDING 3% OF AVAILABLE BALANCE ($45.76), WE CAN CONFIDENTLY ENTER A LONG TRADE HERE. THE TAKE PROFIT IS SET AT SLIGHTLY ABOVE AVERAGE FOR SCALPING OPPORTUNITIES, AND THE STOP LOSS ENSURES PROTECTION AGAINST SUDDEN MARKET REVERSALS WHILE STILL ALLOWING ROOM FOR POTENTIAL GAINS WITHIN OUR RISK TOLERANCE LEVELS.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:43:23,973 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 07:43:23,974 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 22.46s - 5 prompts executed
2025-06-21 07:52:12,915 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:52:12,916 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 07:52:12,919 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 07:52:12,919 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:52:16,383 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 07:52:16,383 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 07:52:16,384 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 07:52:16,384 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 07:52:16,385 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:52:16,386 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:52:16,387 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 07:52:16,387 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:52:20,275 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 524 chars
2025-06-21 07:52:20,276 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that it has been consistently rising without significant support levels or volume indicators to sug...
2025-06-21 07:52:20,276 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 138, Total: 260
2025-06-21 07:52:20,278 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT IT HAS BEEN CONSISTENTLY RISING WITHOUT SIGNIFICANT SUPPORT LEVELS OR VOLUME INDICATORS TO SUGGEST SUSTAINABILITY, WHICH OFTEN PRECEDES SHARP CORRECTIONS. A SHORT POSITION COULD CAPITALIZE ON THIS EXPECTED DECLINE WITH MODERATE RISK MANAGEMENT THROUGH TAKE-PROFIT AND STOP-LOSS ORDERS SET AT 10% AND 20%, RESPECTIVELY.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:52:20,278 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 07:52:20,279 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:52:20,280 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:52:20,280 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162380/$0.162381
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161568
Resistance: $0.163192
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 07:52:20,281 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:52:26,074 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 958 chars
2025-06-21 07:52:26,074 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum rem...
2025-06-21 07:52:26,075 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 259, Total: 784
2025-06-21 07:52:26,077 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF BREACHED. MOMENTUM REMAINS NEUTRAL AND SPREAD CONDITIONS ARE FAVORABLE FOR SHORTING DUE TO LOW BID/ASK SPREADS INDICATING LIQUIDITY WITHOUT SIGNIFICANT SLIPPAGE RISK. THE RISK/REWARD RATIO OF 2:1 JUSTIFIES A SHORT POSITION, WITH THE TAKE PROFIT SET AT 10% ABOVE ENTRY PRICE CONSIDERING POTENTIAL MARKET VOLATILITY AND STOP LOSS PROTECTION ESTABLISHED AT 20% BELOW TO MANAGE DOWNSIDE RISKS EFFECTIVELY.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:52:26,077 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 07:52:26,078 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 07:52:26,078 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 07:52:26,079 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:52:26,079 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:52:26,080 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 07:52:26,081 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:52:31,399 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 885 chars
2025-06-21 07:52:31,399 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 60%
TAKE_PROFIT: 35%
STOP_LOSS: 40%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes. With the confidence in unknown regimes at medium but decreasing due to trending downward wins, it's prud...
2025-06-21 07:52:31,399 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 223, Total: 790
2025-06-21 07:52:31,400 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 60.0, 'EXPLANATION': "THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES. WITH THE CONFIDENCE IN UNKNOWN REGIMES AT MEDIUM BUT DECREASING DUE TO TRENDING DOWNWARD WINS, IT'S PRUDENT TO SHORTEN EXPOSURE BY REDUCING RISK PER TRADE (RISK_ADJUSTMENT SET TO 0.5) WHILE MAINTAINING A REASONABLE HOLD TIME TARGET OF 8 MINUTES AND ENTRY/EXIT THRESHOLDS THAT REFLECT THE CURRENT MARKET UNCERTAINTY BUT STILL ALLOW FOR SOME PROFIT POTENTIAL GIVEN THE SHARPE RATIO IS AT AN ACCEPTABLE LEVEL DESPITE RECENT POOR PERFORMANCE. THE TAKE-PROFIT SHOULD BE HIGHER THAN USUAL DUE TO INCREASED RISK, AIMING AROUND 35%, WITH A STOP LOSS SET SLIGHTLY ABOVE THIS THRESHOLD AT 40% TO PROTECT AGAINST FURTHER LOSSES WHILE STILL CAPTURING SOME UPSIDE IF THE MARKET TURNS FAVORABLE QUICKLY.", 'ACTION': 'ENTER_NOW'}
2025-06-21 07:52:31,401 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 07:52:31,402 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 07:52:31,403 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:52:31,403 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:52:31,404 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162380 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154261 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154261 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154261 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 07:52:31,404 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:52:36,732 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 829 chars
2025-06-21 07:52:36,733 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a clear breakout setup for DOGE/USDT with high confidence and an attractive risk-reward ratio of 1:2.5, which is above the preferred threshold of >2:1. Given that there are two position slot...
2025-06-21 07:52:36,733 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 221, Total: 873
2025-06-21 07:52:36,741 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A CLEAR BREAKOUT SETUP FOR DOGE/USDT WITH HIGH CONFIDENCE AND AN ATTRACTIVE RISK-REWARD RATIO OF 1:2.5, WHICH IS ABOVE THE PREFERRED THRESHOLD OF >2:1. GIVEN THAT THERE ARE TWO POSITION SLOTS AVAILABLE WITHIN OUR REMAINING RISK BUDGET (WHICH ALLOWS US TO TAKE ON UP TO $390 IN TRADES), WE CAN ALLOCATE A PORTION FOR THIS OPPORTUNITY WHILE MAINTAINING ADEQUATE DIVERSIFICATION AND MANAGING OVERALL PORTFOLIO EXPOSURE. THE VOLATILITY LEVEL IS SUITABLE, AS IT FALLS WITHIN THE SCALPING RANGE OF 1-4%, WHICH ALIGNS WITH OUR TRADING STRATEGY THAT FOCUSES ON QUICK PROFITS FROM SHORT-TERM PRICE MOVEMENTS RATHER THAN LONG POSITIONS IN HIGHLY LIQUID ASSETS LIKE BTC/USDT AND ETH/USDT WHERE WE SEE NO SIGNIFICANT SETUP QUALITY.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:52:36,742 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 07:52:36,750 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 23.83s - 5 prompts executed
2025-06-21 07:54:31,784 - main - INFO - Epinnox v6 starting up...
2025-06-21 07:54:31,800 - core.performance_monitor - INFO - Performance monitoring started
2025-06-21 07:54:31,800 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-21 07:54:31,801 - main - INFO - Performance monitoring initialized
2025-06-21 07:54:31,811 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-21 07:54:31,811 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-21 07:54:31,812 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-21 07:54:41,367 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-21 07:54:42,668 - websocket - INFO - Websocket connected
2025-06-21 07:54:45,124 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-21 07:54:45,124 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-21 07:54:45,125 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-21 07:54:45,126 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-21 07:54:45,131 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-21 07:54:47,195 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-21 07:54:47,195 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-21 07:54:47,196 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-21 07:54:47,198 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-21 07:54:47,199 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-21 07:54:47,204 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-21 07:54:47,205 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-21 07:54:47,216 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-21 07:54:47,218 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-21 07:54:47,219 - storage.session_manager - INFO - Session Manager initialized
2025-06-21 07:54:47,225 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250621_075447_9d78c30f
2025-06-21 07:54:47,227 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250621_075447_9d78c30f
2025-06-21 07:54:47,286 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-21 07:57:29,275 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:57:29,275 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 07:57:29,276 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 07:57:29,276 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:57:32,898 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 07:57:32,899 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 07:57:32,899 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 07:57:32,904 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 07:57:32,906 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:57:32,906 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:57:32,910 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 07:57:32,911 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:57:37,142 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 601 chars
2025-06-21 07:57:37,142 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with its price trending upwards despite lackluster fundamentals. Historical volatility is high and shows signs of an impending c...
2025-06-21 07:57:37,143 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 154, Total: 276
2025-06-21 07:57:37,145 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH ITS PRICE TRENDING UPWARDS DESPITE LACKLUSTER FUNDAMENTALS. HISTORICAL VOLATILITY IS HIGH AND SHOWS SIGNS OF AN IMPENDING CORRECTION BASED ON MOVING AVERAGES CROSSING BELOW KEY SUPPORT LEVELS. A SHORT POSITION COULD CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING POTENTIAL LOSSES THROUGH A TAKE-PROFIT AT 10% GAIN, WITH THE STOP LOSS SET TO PROTECT AGAINST SIGNIFICANT MARKET SWINGS BEYOND OUR RISK TOLERANCE THRESHOLD.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:57:37,146 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 07:57:37,146 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:57:37,146 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:57:37,147 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162233/$0.162234
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161422
Resistance: $0.163044
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 07:57:37,148 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:57:42,102 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 869 chars
2025-06-21 07:57:42,102 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-21 07:57:42,103 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 197, Total: 722
2025-06-21 07:57:42,104 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF CONFIRMED BY INCREASED TRADING ACTIVITY (VOLUME CONFIRMATION). GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL AND MOMENTUM DOES NOT SUGGEST AN IMMEDIATE TREND CHANGE, IT'S PRUDENT TO TAKE A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% FOR THE PROFIT TARGET AND 20% STOP LOSS. THE CONFIDENCE LEVEL IS HIGH DUE TO FAVORABLE SPREAD CONDITIONS WHICH COULD IMPLY LOWER TRANSACTION COSTS IF EXECUTED QUICKLY AS MARKET SENTIMENT REMAINS NEUTRAL ACCORDING TO ML ENSEMBLE ANALYSIS, BUT WE ARE WAITING ON VOLUME CONFIRMATION BEFORE ENTERING A LIMIT ORDER WITH SPECIFIED TIMING FACTORS IN MIND.", 'ACTION': 'ENTER_NOW'}
2025-06-21 07:57:42,105 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 07:57:42,107 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 07:57:42,107 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 07:57:42,108 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:57:42,108 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:57:42,108 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 07:57:42,109 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:57:46,438 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 596 chars
2025-06-21 07:57:46,439 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes or increased volatility not yet reflected in the confidence level of unknown regimes. A short position...
2025-06-21 07:57:46,439 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 146, Total: 713
2025-06-21 07:57:46,440 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES OR INCREASED VOLATILITY NOT YET REFLECTED IN THE CONFIDENCE LEVEL OF UNKNOWN REGIMES. A SHORT POSITION REDUCES EXPOSURE TO FURTHER LOSSES WHILE AWAITING CLEARER SIGNALS FOR LONG POSITIONS. ADJUSTED RISK MULTIPLIER IS SET LOWER DUE TO UNCERTAINTY BUT STILL ALLOWS SOME ROOM FOR PROFIT IF A QUICK REVERSAL OCCURS, AS SUGGESTED BY SCALP SUITABILITY BEING MEDIUM AND MARKET VOLATILITY REMAINING NORMAL.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:57:46,441 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 07:57:46,441 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 07:57:46,442 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 07:57:46,442 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 07:57:46,443 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162233 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154121 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154121 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154121 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 07:57:46,443 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 07:57:51,218 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 686 chars
2025-06-21 07:57:51,218 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 80%
TAKE_PROFIT: 2.5%
STOP_LOSS: 1%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget is low...
2025-06-21 07:57:51,218 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 179, Total: 831
2025-06-21 07:57:51,220 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, SUGGESTING STRONG BUYING MOMENTUM IN THE SHORT TERM (5-15 MINUTES). GIVEN THAT OUR RISK BUDGET IS LOW AT ONLY 1%, WE SHOULD AIM TO CAPITALIZE ON THIS WITHOUT OVEREXTENDING. THE TAKE PROFIT OF 2.5% ALIGNS WELL WITHIN A FAVORABLE REWARD/RISK RATIO WHILE ENSURING ADEQUATE PROTECTION WITH STOP LOSS SET AT 1%. THIS STRATEGY TAKES ADVANTAGE OF THE CURRENT MARKET CONDITIONS AND OUR AVAILABLE ACCOUNT CAPACITY, WHICH ALLOWS FOR TWO POSITIONS TO BE OPENED SAFELY WITHOUT EXCEEDING RISK LIMITS.', 'ACTION': 'ENTER_NOW'}
2025-06-21 07:57:51,220 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 07:57:51,221 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 21.95s - 5 prompts executed
2025-06-21 08:06:23,777 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:06:23,778 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 08:06:23,778 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 08:06:23,779 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:06:27,340 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 08:06:27,341 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 08:06:27,342 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 08:06:27,343 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 08:06:27,344 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:06:27,344 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:06:27,345 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 08:06:27,346 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:06:31,625 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 651 chars
2025-06-21 08:06:31,625 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that this could be an inflated bubble simil...
2025-06-21 08:06:31,625 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 166, Total: 288
2025-06-21 08:06:31,627 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT THIS COULD BE AN INFLATED BUBBLE SIMILAR TO PREVIOUS ONES SEEN WITHIN THE CRYPTO MARKETS. A SHORT POSITION WOULD CAPITALIZE ON EXPECTED CORRECTIONS WHILE LIMITING RISK EXPOSURE THROUGH A TAKE-PROFIT AT 10% ABOVE CURRENT LEVELS, WITH STOP LOSS SET AT 20% BELOW IN CASE OF SUDDEN MARKET REVERSALS OR UNEXPECTED NEWS THAT COULD FURTHER INFLATE PRICES TEMPORARILY BEFORE THE CORRECTION OCCURS.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:06:31,627 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 08:06:31,628 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:06:31,628 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:06:31,628 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.162475/$0.162476
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.161663
Resistance: $0.163287
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 08:06:31,629 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:06:36,993 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 824 chars
2025-06-21 08:06:36,994 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-21 08:06:36,994 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 229, Total: 754
2025-06-21 08:06:36,995 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'ACTION': 'ENTER_NOW', 'CONFIDENCE': 75.0, 'ENTRY_TYPE': 'MARKET', 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF BREACHED. MOMENTUM AND ORDERFLOW ARE NEUTRAL BUT DO NOT PROVIDE STRONG BULLISH SIGNALS EITHER. GIVEN THAT THERE'S AN UNFAVORABLE SPREAD CONDITION (0.100%), IT WOULD BE PRUDENT TO ENTER A SHORT POSITION WITH MODERATE CONFIDENCE, AIMING FOR A TAKE-PROFIT AT 10% IF THE PRICE RISES TOWARDS RESISTANCE AND SETTING STOP LOSS SLIGHTLY BELOW SUPPORT LEVEL ($0.1614) AS AN ADDITIONAL SAFEGUARD AGAINST POTENTIAL DOWNSIDE RISK IN CASE OF UNEXPECTED MARKET MOVEMENTS OR INCREASED VOLATILITY."}
2025-06-21 08:06:36,995 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (MARKET order)
2025-06-21 08:06:36,996 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (MARKET order)
2025-06-21 08:06:36,996 - core.llm_action_executors - INFO - Market entry: DOGE/USDT:USDT buy 10.0
2025-06-21 08:06:36,996 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:06:36,998 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:06:36,998 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 08:06:36,999 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:06:41,502 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 665 chars
2025-06-21 08:06:41,502 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.20%
STOP_LOSS: -3.00%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes or increased volatility not yet reflected in the confidence level of unknown regimes. A short positi...
2025-06-21 08:06:41,503 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 163, Total: 730
2025-06-21 08:06:41,503 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES OR INCREASED VOLATILITY NOT YET REFLECTED IN THE CONFIDENCE LEVEL OF UNKNOWN REGIMES. A SHORT POSITION COULD CAPITALIZE ON THIS TREND WITH AN ADJUSTED RISK MULTIPLIER TO MITIGATE LOSSES IF FURTHER DOWNTRENDS OCCUR. THE TAKE PROFIT IS SET HIGHER THAN AVERAGE LOSS DUE TO RECENT POOR PERFORMANCE, AND A LARGER STOP-LOSS PERCENTAGE ACCOUNTS FOR POTENTIAL INCREASED VOLATILITY OR ADVERSE REGIME SHIFTS THAT HAVE NOT YET BEEN DETECTED BY THE MARKET CONFIDENCE INDICATOR.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:06:41,504 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 08:06:41,504 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 08:06:41,506 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:06:41,508 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:06:41,508 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.162475 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.154351 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.154351 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.154351 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 08:06:41,509 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:06:47,103 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 902 chars
2025-06-21 08:06:47,103 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.0%
EXPLANATION: The opportunity ranking indicates a clear breakout setup for DOGE/USDT with high confidence and an appropriate risk-reward ratio of 1:2.5, which is above the preferred threshold of >2:1. Given that there are two position s...
2025-06-21 08:06:47,104 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 242, Total: 894
2025-06-21 08:06:47,105 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A CLEAR BREAKOUT SETUP FOR DOGE/USDT WITH HIGH CONFIDENCE AND AN APPROPRIATE RISK-REWARD RATIO OF 1:2.5, WHICH IS ABOVE THE PREFERRED THRESHOLD OF >2:1. GIVEN THAT THERE ARE TWO POSITION SLOTS AVAILABLE WITHIN OUR REMAINING RISK BUDGET (WHICH ALLOWS US TO TAKE ON UP TO $0.74 IN TRADES), WE CAN ALLOCATE A PORTION FOR DOGE/USDT WHILE MAINTAINING DIVERSIFICATION WITH BTC/USDT, ETH/USDT, AND SOL/USDT AS THEY ALL SHARE THE SAME SETUP TYPE OF RANGE BREAKOUT BUT HAVE NOT BEEN HIGHLIGHTED SPECIFICALLY LIKE DOGE/USDT. THE LIQUIDITY IS GOOD ACROSS THESE SYMBOLS (<0.3% SPREADS), WHICH SHOULD FACILITATE SMOOTH ENTRY AND EXIT FROM POSITIONS WITHOUT SIGNIFICANT SLIPPAGE, ESPECIALLY IMPORTANT IN A MODERATE RISK ENVIRONMENT WHERE WE AIM TO PRESERVE CAPITAL WHILE SEEKING PROFITABLE OPPORTUNITIES.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:06:47,105 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 08:06:47,106 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 23.33s - 5 prompts executed
2025-06-21 08:49:04,308 - main - INFO - Epinnox v6 starting up...
2025-06-21 08:49:04,323 - core.performance_monitor - INFO - Performance monitoring started
2025-06-21 08:49:04,323 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-21 08:49:04,324 - main - INFO - Performance monitoring initialized
2025-06-21 08:49:04,332 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-21 08:49:04,332 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-21 08:49:04,334 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-21 08:49:14,321 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-21 08:49:15,578 - websocket - INFO - Websocket connected
2025-06-21 08:49:19,042 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-21 08:49:19,043 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-21 08:49:19,044 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-21 08:49:19,044 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-21 08:49:19,049 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-21 08:49:21,122 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-21 08:49:21,123 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-21 08:49:21,123 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-21 08:49:21,125 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-21 08:49:21,125 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-21 08:49:21,128 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-21 08:49:21,128 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-21 08:49:21,139 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-21 08:49:21,141 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-21 08:49:21,141 - storage.session_manager - INFO - Session Manager initialized
2025-06-21 08:49:21,148 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250621_084921_4e5b40f8
2025-06-21 08:49:21,151 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250621_084921_4e5b40f8
2025-06-21 08:49:21,209 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-21 08:53:44,265 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:53:44,266 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 08:53:44,267 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 08:53:44,268 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:53:47,774 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 08:53:47,775 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 08:53:47,775 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 08:53:47,778 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 08:53:47,778 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:53:47,778 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:53:47,779 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 08:53:47,779 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:53:52,056 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 590 chars
2025-06-21 08:53:52,056 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that similar patterns have led to significant corrections within six months, suggesting an impendin...
2025-06-21 08:53:52,057 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 148, Total: 270
2025-06-21 08:53:52,058 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS ANALYSIS AND CONSIDERING TYPICAL VOLATILITY LEVELS FOR SUCH ASSETS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT AND 20% STOP LOSS TO CAPITALIZE ON THE EXPECTED PRICE DECLINE WHILE MANAGING POTENTIAL LOSSES EFFECTIVELY.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:53:52,059 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 08:53:52,059 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:53:52,060 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:53:52,060 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.160650/$0.160655
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.159847
Resistance: $0.161453
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 08:53:52,060 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:53:58,760 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1339 chars
2025-06-21 08:53:58,760 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, despite this balance and favorable spread conditions, technical indicators such as momentu...
2025-06-21 08:53:58,761 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 295, Total: 820
2025-06-21 08:53:58,762 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, DESPITE THIS BALANCE AND FAVORABLE SPREAD CONDITIONS, TECHNICAL INDICATORS SUCH AS MOMENTUM ARE NEUTRAL WHICH IMPLIES THAT THERE IS NOT ENOUGH BULLISH OR BEARISH PRESSURE TO CONFIDENTLY PREDICT AN IMMEDIATE DIRECTIONAL MOVE. THE KEY LEVELS SHOW SUPPORT AT $0.159847 WITH RESISTANCE SLIGHTLY ABOVE IT; HOWEVER, THE DISTANCE FROM CURRENT PRICE SUGGESTS A POTENTIAL FOR DOWNWARD MOVEMENT WITHOUT SIGNIFICANT RISK OF BREAKING THROUGH THESE CRITICAL POINTS IMMEDIATELY. GIVEN THAT THERE IS NO CLEAR SIGNAL IN TERMS OF VOLUME CONFIRMATION OR MOMENTUM ALIGNMENT AND CONSIDERING THE NEUTRAL ML ENSEMBLE PREDICTION ALONG WITH FAVORABLE SPREAD CONDITIONS (WHICH COULD INDICATE LIQUIDITY), ENTERING NOW ON SHORT POSITION SEEMS PRUDENT, ALBEIT CAUTIOUSLY DUE TO POTENTIAL VOLATILITY INDICATED BY A RISK/REWARD RATIO OF 2:1. THE TAKE-PROFIT IS SET AT 10% ABOVE THE ENTRY PRICE AS AN INCENTIVE FOR QUICK GAINS IF MARKET SENTIMENT SHIFTS POSITIVELY UNEXPECTEDLY AND STOP LOSS IS PLACED SLIGHTLY BELOW SUPPORT LEVEL, PROVIDING PROTECTION AGAINST SIGNIFICANT DOWNSIDE RISKS WHILE ALLOWING ROOM TO CAPTURE POTENTIAL UPSIDE SHOULD A REVERSAL OCCUR.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:53:58,762 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 08:53:58,762 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 08:53:58,764 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 08:53:58,765 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:53:58,766 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:53:58,766 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 08:53:58,767 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:54:03,334 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 605 chars
2025-06-21 08:54:03,335 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy is showing a declining win rate and poor recent performance, which indicates the need for adjustment despite maintaining normal market volatility levels. A risk reduction of half will help mitigate p...
2025-06-21 08:54:03,335 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 149, Total: 716
2025-06-21 08:54:03,336 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY IS SHOWING A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, WHICH INDICATES THE NEED FOR ADJUSTMENT DESPITE MAINTAINING NORMAL MARKET VOLATILITY LEVELS. A RISK REDUCTION OF HALF WILL HELP MITIGATE POTENTIAL LOSSES WHILE STILL ALLOWING SOME EXPOSURE TO PROFITABLE OPPORTUNITIES GIVEN THAT CONFIDENCE IN REGIME IDENTIFICATION REMAINS AT 50%. THE TAKE-PROFIT IS SET HIGHER THAN AVERAGE PROFIT DUE TO INCREASED CAUTION, AND THE STOP LOSS DEEPER INTO NEGATIVE TERRITORY REFLECTS A MORE CONSERVATIVE APPROACH.', 'ACTION': 'ENTER_NOW'}
2025-06-21 08:54:03,337 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 08:54:03,337 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 08:54:03,338 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:54:03,339 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:54:03,339 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.160650 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.152617 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.152617 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.152617 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 08:54:03,341 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:54:08,315 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 648 chars
2025-06-21 08:54:08,315 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The best opportunity is DOGE/USDT with a high setup quality indicating clear breakout patterns and strong signals, which aligns well with the current market momentum that's neutral but has good liquidity as indicated by lo...
2025-06-21 08:54:08,316 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 176, Total: 828
2025-06-21 08:54:08,317 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE BEST OPPORTUNITY IS DOGE/USDT WITH A HIGH SETUP QUALITY INDICATING CLEAR BREAKOUT PATTERNS AND STRONG SIGNALS, WHICH ALIGNS WELL WITH THE CURRENT MARKET MOMENTUM THAT'S NEUTRAL BUT HAS GOOD LIQUIDITY AS INDICATED BY LOW SPREADS (<0.3%). GIVEN ITS 1-4% VOLATILITY RANGE SUITABLE FOR SCALPING STRATEGIES, IT FITS WITHIN OUR RISK/REWARD PREFERENCE OF >2:1 RATIO AND WE HAVE SUFFICIENT ACCOUNT CAPACITY TO TAKE A POSITION WITHOUT EXCEEDING THE REMAINING RISK BUDGET. THE TIME HORIZON IS SHORT AT 5-15 MINUTES WHICH ALLOWS US TO REACT QUICKLY IF MARKET CONDITIONS CHANGE.", 'ACTION': 'ENTER_NOW'}
2025-06-21 08:54:08,317 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 08:54:08,318 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 24.05s - 5 prompts executed
2025-06-21 08:59:54,078 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:59:54,079 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 08:59:54,079 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 08:59:54,080 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 08:59:57,615 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 08:59:57,615 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 08:59:57,616 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 08:59:57,616 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 08:59:57,617 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 08:59:57,617 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 08:59:57,618 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 08:59:57,618 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:00:02,118 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 697 chars
2025-06-21 09:00:02,119 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant corrections within six months, suggesting an impending down...
2025-06-21 09:00:02,119 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 169, Total: 291
2025-06-21 09:00:02,123 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS ANALYSIS AND CONSIDERING MY CONFIDENCE LEVEL BASED ON TECHNICAL INDICATORS SUCH AS MOVING AVERAGES CROSSING BELOW KEY RESISTANCE LEVELS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE-PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES EARLY BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF FURTHER DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:00:02,123 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 09:00:02,124 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:00:02,124 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:00:02,125 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.160517/$0.160518
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.159714
Resistance: $0.161320
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 09:00:02,125 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:00:08,215 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 990 chars
2025-06-21 09:00:08,216 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-21 09:00:08,216 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 270, Total: 795
2025-06-21 09:00:08,218 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF CONFIRMED BY INCREASED TRADING ACTIVITY (VOLUME CONFIRMATION). GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL AND MOMENTUM DOES NOT SUGGEST AN IMMEDIATE TREND CHANGE, IT'S PRUDENT TO TAKE A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% FOR THE PROFIT TARGET AND 20% STOP LOSS. THE CONFIDENCE LEVEL IS HIGH DUE TO FAVORABLE SPREAD CONDITIONS BUT TEMPERED BY PENDING VOLUME CONFIRMATION WHICH COULD PROVIDE ADDITIONAL SUPPORT OR SIGNAL AN IMPENDING TREND REVERSAL, HENCE NOT WAITING FURTHER THAN A SHORT-TERM LIMIT ORDER WITH MARKET ENTRY TYPE SET AT LIMIT.", 'ACTION': 'ENTER_NOW'}
2025-06-21 09:00:08,219 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 09:00:08,220 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 09:00:08,221 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 09:00:08,222 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:00:08,223 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:00:08,223 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 09:00:08,225 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:00:12,999 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 654 chars
2025-06-21 09:00:12,999 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.20%
STOP_LOSS: -3.00%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, indicating potential market regime changes or increased volatility not yet reflected in the confidence level of unknown regimes. A short positi...
2025-06-21 09:00:13,000 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 162, Total: 729
2025-06-21 09:00:13,001 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, INDICATING POTENTIAL MARKET REGIME CHANGES OR INCREASED VOLATILITY NOT YET REFLECTED IN THE CONFIDENCE LEVEL OF UNKNOWN REGIMES. A SHORT POSITION REDUCES EXPOSURE TO FURTHER LOSSES WHILE AWAITING CLEARER SIGNALS FOR LONG POSITIONS. ADJUSTED RISK MULTIPLIER IS SET AT 0.5 DUE TO DECREASED TREND RELIABILITY AND A CONSERVATIVE HOLD TIME TARGET REFLECTS MARKET UNCERTAINTY, WITH ENTRY THRESHOLD SLIGHTLY LOWERED IN ANTICIPATION OF POTENTIAL DOWNTRENDS BUT STILL WITHIN MEDIUM SCALP SUITABILITY RANGE.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:00:13,002 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 09:00:13,002 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 09:00:13,003 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:00:13,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:00:13,004 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.160517 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.152491 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.152491 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.152491 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 09:00:13,006 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:00:17,698 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 575 chars
2025-06-21 09:00:17,698 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 75%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, which aligns well with the momentum criteria of our analysis. Given that there is no significant ch...
2025-06-21 09:00:17,699 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 151, Total: 803
2025-06-21 09:00:17,701 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, WHICH ALIGNS WELL WITH THE MOMENTUM CRITERIA OF OUR ANALYSIS. GIVEN THAT THERE IS NO SIGNIFICANT CHANGE IN MOM (MOVING AVERAGE), VOLATILITY REMAINS NORMAL SUITABLE FOR SCALPING STRATEGIES WITHIN 1-4%. THE RISK BUDGET ALLOWS A SMALL POSITION SIZE WITHOUT EXCEEDING IT, MAINTAINING MODERATE EXPOSURE TO MARKET MOVEMENTS WHILE CAPITALIZING ON POTENTIAL GAINS FROM THE BREAKOUT.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:00:17,701 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 09:00:17,702 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 23.62s - 5 prompts executed
2025-06-21 09:05:32,235 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:05:32,235 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:05:32,236 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 09:05:32,238 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:05:35,695 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 09:05:35,695 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 09:05:35,695 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 09:05:35,697 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 09:05:35,698 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:05:35,698 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:05:35,699 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 09:05:35,699 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:05:40,036 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 602 chars
2025-06-21 09:05:40,037 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within two weeks, suggesting an imminent ...
2025-06-21 09:05:40,037 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 159, Total: 281
2025-06-21 09:05:40,038 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN TWO WEEKS, SUGGESTING AN IMMINENT DECLINE. A SHORT POSITION WOULD CAPITALIZE ON THIS EXPECTED DROP WHILE LIMITING RISK WITH STOP-LOSS ORDERS SET AT 20% BELOW ENTRY POINT AND A TAKE PROFIT TARGETED AT ACHIEVING GAINS IF THE MARKET REVERSES EARLY DUE TO UNFORESEEN POSITIVE NEWS OR EVENTS THAT COULD TEMPORARILY BOOST PRICES BY UP TO 10%.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:05:40,038 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 09:05:40,040 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:05:40,040 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:05:40,041 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.160917/$0.160918
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.160112
Resistance: $0.161722
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 09:05:40,041 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:05:46,092 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1048 chars
2025-06-21 09:05:46,093 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. Although the price is near key support levels and momentum remains neutral, there's an opportunity to capitalize on pote...
2025-06-21 09:05:46,093 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 259, Total: 784
2025-06-21 09:05:46,095 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. ALTHOUGH THE PRICE IS NEAR KEY SUPPORT LEVELS AND MOMENTUM REMAINS NEUTRAL, THERE'S AN OPPORTUNITY TO CAPITALIZE ON POTENTIAL DOWNWARD PRESSURE DUE TO L2 IMBALANCE BEING AT ZERO PERCENT WHICH COULD INDICATE LIQUIDITY ISSUES OR LACK OF INTEREST IN BUYING HIGHER PRICES. THE FAVORABLE SPREAD CONDITIONS FURTHER ENHANCE THIS CASE FOR A SHORT POSITION AS IT IMPLIES LOWER TRANSACTION COSTS AND BETTER EXECUTION QUALITY, INCREASING THE ATTRACTIVENESS OF ENTERING NOW WITH MODERATE CONFIDENCE LEVELS GIVEN THAT VOLUME CONFIRMATION IS PENDING BUT OTHER FACTORS ARE ALIGNED TOWARDS POTENTIAL DOWNWARD MOVEMENT.", 'ACTION': 'ENTER_NOW'}
2025-06-21 09:05:46,096 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 09:05:46,097 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 09:05:46,097 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 09:05:46,098 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:05:46,098 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:05:46,099 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 09:05:46,100 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:05:51,134 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 719 chars
2025-06-21 09:05:51,135 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.4%
EXPLANATION: The current strategy has a declining win rate and poor recent performance, suggesting the need for adjustment despite normal market volatility levels. A risk reduction of half (RISK_ADJUSTMENT=0.5) is recommended to miti...
2025-06-21 09:05:51,135 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 184, Total: 751
2025-06-21 09:05:51,137 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY HAS A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, SUGGESTING THE NEED FOR ADJUSTMENT DESPITE NORMAL MARKET VOLATILITY LEVELS. A RISK REDUCTION OF HALF (RISK_ADJUSTMENT=0.5) IS RECOMMENDED TO MITIGATE POTENTIAL LOSSES WITHOUT DRASTIC CHANGES THAT COULD DISRUPT PROVEN MECHANICS LIKE ENTRY THRESHOLD CONSISTENCY AT 70%. THE HOLD TIME TARGET REMAINS UNCHANGED AS IT ALIGNS WITH SCALP SUITABILITY, BUT THE TAKE-PROFIT AND STOP LOSS ARE ADJUSTED SLIGHTLY HIGHER DUE TO INCREASED RISK. THIS STRATEGY AIMS FOR BETTER CONTROL OVER POTENTIAL LOSSES WHILE STILL CAPITALIZING ON MARKET MOVEMENTS WITHIN ACCEPTABLE THRESHOLDS.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:05:51,138 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 09:05:51,138 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 09:05:51,139 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:05:51,139 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:05:51,140 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.160917 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.152871 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.152871 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.152871 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 09:05:51,141 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:05:55,787 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 590 chars
2025-06-21 09:05:55,788 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 75%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.0%
EXPLANATION: The opportunity ranking indicates a high-quality setup for DOGE/USDT with clear breakout patterns and volume confirmation, suggesting strong buying momentum in the short term (5-15 minutes). Given that our risk budget allo...
2025-06-21 09:05:55,788 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 652, Completion: 163, Total: 815
2025-06-21 09:05:55,790 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE OPPORTUNITY RANKING INDICATES A HIGH-QUALITY SETUP FOR DOGE/USDT WITH CLEAR BREAKOUT PATTERNS AND VOLUME CONFIRMATION, SUGGESTING STRONG BUYING MOMENTUM IN THE SHORT TERM (5-15 MINUTES). GIVEN THAT OUR RISK BUDGET ALLOWS US TO TAKE ON THIS POSITION WITHOUT EXCEEDING 3.0% OF AVAILABLE BALANCE, WE CAN CONFIDENTLY ENTER A LONG TRADE HERE. THE TAKE PROFIT IS SET AT DOUBLE THE STOP LOSS LEVEL AS PER OPPORTUNITY CRITERIA AND ALIGNS WITH TYPICAL SCALPING STRATEGIES FOR VOLATILITY IN THE RANGE SPECIFIED (1-4%).', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:05:55,790 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-21 09:05:55,791 - core.llm_orchestrator - INFO - LLM prompt cycle completed in 23.56s - 5 prompts executed
2025-06-21 09:15:22,528 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:15:22,528 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:15:22,528 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.100%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
- TRENDING_BULL: Strong upward momentum, high volume, clear direction
- TRENDING_BEAR: Strong downward momentum, high volume, clear direction
- RANGING_TIGHT: Low volatility, narrow pric...
2025-06-21 09:15:22,529 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:15:26,087 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 129 chars
2025-06-21 09:15:26,088 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m"
}
```...
2025-06-21 09:15:26,088 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 605, Completion: 72, Total: 677
2025-06-21 09:15:26,089 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-06-21 09:15:26,090 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:15:26,090 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:15:26,090 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-21 09:15:26,091 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:15:30,811 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 762 chars
2025-06-21 09:15:30,812 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may start to decline, as indicated by...
2025-06-21 09:15:30,812 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 184, Total: 306
2025-06-21 09:15:30,813 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SUGGEST THAT WE ARE APPROACHING AN INFLECTION POINT WHERE PRICES MAY START TO DECLINE, AS INDICATED BY RECENT BEARISH CANDLESTICK FORMATIONS AND INCREASING VOLUME ON SELLING DAYS. GIVEN THESE INDICATORS ALONG WITH COMPARATIVE ANALYSIS SHOWING THIS ASSET'S VALUATION HIGHER THAN ITS FUNDAMENTALS WOULD JUSTIFY, A SHORT POSITION IS RECOMMENDED FOR POTENTIAL PROFIT FROM AN ANTICIPATED PRICE CORRECTION. THE TAKE-PROFIT LEVEL HAS BEEN SET AT 10% TO CAPITALIZE ON THE EXPECTED DOWNWARD MOVEMENT WHILE ENSURING RISK MANAGEMENT THROUGH STOP LOSS PROTECTION OF UP TO 20%.", 'ACTION': 'ENTER_NOW'}
2025-06-21 09:15:30,815 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-21 09:15:30,815 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:15:30,816 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:15:30,816 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.160950/$0.160962
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.160145
Resistance: $0.161755
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-21 09:15:30,817 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:15:36,413 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 946 chars
2025-06-21 09:15:36,414 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if breached. Momentum and...
2025-06-21 09:15:36,415 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 228, Total: 753
2025-06-21 09:15:36,416 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF BREACHED. MOMENTUM AND ORDERFLOW ARE NEUTRAL BUT DO NOT PROVIDE STRONG SIGNALS FOR IMMEDIATE ACTION. GIVEN THAT THERE'S FAVORABLE SPREAD CONDITIONS AND WE HAVE AN ACCEPTABLE RISK/REWARD RATIO OF 2:1 WITH A MODERATE CONFIDENCE LEVEL, IT IS ADVISABLE TO INITIATE A SHORT POSITION NOW RATHER THAN WAIT FURTHER AS THE MARKET DATA DOES NOT STRONGLY SUGGEST SIGNIFICANT UPCOMING MOVEMENT IN EITHER DIRECTION WITHIN OUR WAITING PERIOD. THE TAKE-PROFIT AT 10% AND STOP LOSS SET AT 20% PROVIDE AN ADEQUATE RISK MANAGEMENT STRATEGY WHILE STILL ALLOWING FOR POTENTIAL GAINS IF PRICE MOVES AGAINST US, ALBEIT WITH A CAUTIOUS APPROACH DUE TO THE NEUTRAL NATURE OF OTHER INDICATORS.", 'ACTION': 'ENTER_NOW'}
2025-06-21 09:15:36,416 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-21 09:15:36,418 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-21 09:15:36,418 - core.llm_action_executors - INFO - Limit entry: DOGE/USDT:USDT buy 10.0
2025-06-21 09:15:36,419 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:15:36,419 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:15:36,420 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-06-21 09:15:36,421 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:15:41,616 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 793 chars
2025-06-21 09:15:41,617 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 65%
TAKE_PROFIT: 1.2%
STOP_LOSS: -0.8%
EXPLANATION: The current strategy is showing a declining win rate and poor recent performance, which indicates the need for adaptation despite maintaining normal market volatility levels. By reducing risk per trade (RISK_ADJUSTMENT t...
2025-06-21 09:15:41,617 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 567, Completion: 198, Total: 765
2025-06-21 09:15:41,618 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 65.0, 'EXPLANATION': 'THE CURRENT STRATEGY IS SHOWING A DECLINING WIN RATE AND POOR RECENT PERFORMANCE, WHICH INDICATES THE NEED FOR ADAPTATION DESPITE MAINTAINING NORMAL MARKET VOLATILITY LEVELS. BY REDUCING RISK PER TRADE (RISK_ADJUSTMENT TO 0.5), WE CAN MITIGATE POTENTIAL LOSSES WHILE STILL CAPITALIZING ON SHORT-TERM OPPORTUNITIES GIVEN THE MEDIUM SCALP SUITABILITY OF THIS REGIME AND A MODERATELY HIGH CONFIDENCE LEVEL IN ITS CURRENT STATE. THE TAKE PROFIT IS SET HIGHER THAN AVERAGE DUE TO INCREASED RISK, AIMING FOR BETTER RETURNS IF TRENDS IMPROVE OR REMAIN STABLE. A STOP LOSS AT -0.8% PROVIDES PROTECTION AGAINST SIGNIFICANT DOWNTURNS WITHOUT BEING OVERLY CONSERVATIVE GIVEN THE NORMAL DRAWDOWN LEVELS AND MARKET CONDITIONS.', 'ACTION': 'ENTER_NOW'}
2025-06-21 09:15:41,619 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-06-21 09:15:41,619 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-06-21 09:15:41,620 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:15:41,620 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:15:41,621 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 OPPORTUNITY SCANNER - Multi-Symbol Analysis

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.160950 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.152902 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.152902 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.152902 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM

🎯 MARKET OVERVIEW:
Sector Momentum: NEUTRAL
Overall Volatility: NORMAL
Volume Profile: AVERAGE
Risk Environment: MODERATE

💰 ACCOUNT CAPACITY:
Available Balance...
2025-06-21 09:15:41,622 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-21 09:15:51,342 - main - INFO - Epinnox v6 starting up...
2025-06-21 09:15:51,357 - core.performance_monitor - INFO - Performance monitoring started
2025-06-21 09:15:51,358 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-21 09:15:51,359 - main - INFO - Performance monitoring initialized
2025-06-21 09:15:51,368 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-21 09:15:51,368 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-21 09:15:51,369 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-21 09:16:01,502 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-21 09:16:02,765 - websocket - INFO - Websocket connected
2025-06-21 09:16:05,444 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-21 09:16:05,444 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-21 09:16:05,445 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-21 09:16:05,445 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-21 09:16:05,450 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-21 09:16:07,519 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-21 09:16:07,520 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-21 09:16:07,521 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-21 09:16:07,521 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-21 09:16:07,522 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-21 09:16:07,525 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-21 09:16:07,525 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-21 09:16:07,536 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-21 09:16:07,538 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-21 09:16:07,538 - storage.session_manager - INFO - Session Manager initialized
2025-06-21 09:16:07,544 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250621_091607_6c2db2b2
2025-06-21 09:16:07,549 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250621_091607_6c2db2b2
2025-06-21 09:16:07,607 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-21 09:16:23,012 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:16:23,013 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:16:23,014 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160955
ML Predictions: ['WAIT', 'WAIT', 'SHORT', 'LONG', 'LONG', 'LONG', 'SHORT', 'LONG']
ML Confidences: ['64.7%', '60.7%', '71.5%', '86.9%', '65.4%', '67.7%', '77.7%', '60.1%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:16:23,015 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:16:27,518 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 603 chars
2025-06-21 09:16:27,519 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 73%
REASONING: The majority of the market predictions from ML models suggest 'LONG' positions, with high confidence scores above 65%. Additionally, given that there is no consensus for a sell signal (SHORT), and some indicators favor buying ('WAIT'), it would be reasonable...
2025-06-21 09:16:27,519 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 142, Total: 466
2025-06-21 09:16:29,003 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:16:29,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:16:29,004 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×8103 A:0.16×5181
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 69%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:16:29,004 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:16:34,120 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 791 chars
2025-06-21 09:16:34,121 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$4.1", // Using $4 for the purchase amount (3% of balance) and rounding to nearest cent, assuming no fractional crypto units are allowed in this context. Adjust as necessary based on actual trading platform rules regarding currency representation or un...
2025-06-21 09:16:34,121 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 199, Total: 555
2025-06-21 09:17:36,651 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:17:36,652 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:17:36,652 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160749
ML Predictions: ['SHORT', 'SHORT', 'WAIT', 'LONG', 'WAIT', 'LONG', 'WAIT', 'SHORT']
ML Confidences: ['79.7%', '82.7%', '63.3%', '80.6%', '72.7%', '84.2%', '76.5%', '63.1%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:17:36,653 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:17:41,726 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 863 chars
2025-06-21 09:17:41,726 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 83%   
REASONING: The current price of DOGE/USDT shows a slight upward trend despite the mixed ML predictions. Two out of four recent forecasts indicate 'LONG' with high confidence scores (80.6% and 84.2%). Additionally, these two instances are closely followed by lower-...
2025-06-21 09:17:41,726 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 199, Total: 523
2025-06-21 09:17:43,726 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:17:43,727 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:17:43,728 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×6220 A:0.16×1890
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 75%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:17:43,729 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:17:46,626 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:17:46,627 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:17:46,628 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 33, Total: 389
2025-06-21 09:18:49,467 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:18:49,468 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:18:49,468 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160879
ML Predictions: ['SHORT', 'LONG', 'LONG', 'WAIT', 'WAIT', 'SHORT', 'WAIT', 'LONG']
ML Confidences: ['84.3%', '60.2%', '67.6%', '66.9%', '70.4%', '71.8%', '87.4%', '87.3%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:18:49,469 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:18:53,707 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 540 chars
2025-06-21 09:18:53,707 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 75%
REASONING: The majority of the ML predictions are leaning towards 'LONG' or a wait, suggesting that there is potential for an increase in price. Additionally, with high confidence levels (84-87%) on these recommendations and considering market sentiment trends could be...
2025-06-21 09:18:53,709 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 125, Total: 449
2025-06-21 09:18:55,230 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:18:55,231 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:18:55,231 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.01%)
DEPTH: B:0.16×10380 A:0.16×1890
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 74%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:18:55,232 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:19:00,496 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 666 chars
2025-06-21 09:19:00,497 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$41 * (3/100)", // Calculating based on the balance and risk percentage of +/-2% for position sizing, which is within our specified range. Using a conservative approach with 3%.
    "LEVERAGE": 5, // Choosing an average leverage value from the given ra...
2025-06-21 09:19:00,497 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 357, Completion: 184, Total: 541
2025-06-21 09:20:04,952 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:20:04,953 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:20:04,953 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160788
ML Predictions: ['WAIT', 'WAIT', 'WAIT', 'LONG', 'SHORT', 'LONG', 'WAIT', 'WAIT']
ML Confidences: ['68.2%', '75.8%', '72.5%', '73.8%', '87.1%', '77.7%', '80.2%', '66.4%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:20:04,954 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:20:09,523 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 717 chars
2025-06-21 09:20:09,524 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
REASONING: The ML predictions show a mix of 'WAIT' and directional signals, with two out of three recent points suggesting either holding or shorting. Given the current price is relatively high compared to historical volatility patterns indicated by lower confidence l...
2025-06-21 09:20:09,524 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 165, Total: 489
2025-06-21 09:20:11,007 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:20:11,008 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:20:11,008 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.01%)
DEPTH: B:0.16×1890 A:0.16×1890
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 75%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:20:11,010 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:20:16,332 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 752 chars
2025-06-21 09:20:16,333 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$4.1", // Assuming the user wants to spend $4 of their balance, which is within a reasonable risk percentage (2%) and leaves some for potential fees or slippage in live trading scenarios. Adjust as necessary based on actual market conditions at executi...
2025-06-21 09:20:16,333 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 199, Total: 555
2025-06-21 09:21:20,128 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:21:20,128 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:21:20,129 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.161131
ML Predictions: ['WAIT', 'WAIT', 'WAIT', 'LONG', 'SHORT', 'LONG', 'LONG', 'WAIT']
ML Confidences: ['76.1%', '67.9%', '64.7%', '83.8%', '61.0%', '61.9%', '76.5%', '74.2%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:21:20,130 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:21:24,238 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 514 chars
2025-06-21 09:21:24,239 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 82%  
REASONING: Despite some ML predictions indicating a 'WAIT' or 'SHORT', the majority show either 'LONG' (4 out of 7) with high confidence levels, particularly at 83.8% and above for two instances. This indicates that there is strong consensus among models favoring b...
2025-06-21 09:21:24,239 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 133, Total: 457
2025-06-21 09:21:25,799 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:21:25,800 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:21:25,800 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×6204 A:0.16×5551
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 71%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:21:25,804 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:21:28,783 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:21:28,784 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:21:28,785 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 33, Total: 389
2025-06-21 09:22:32,064 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:22:32,065 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:22:32,065 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160762
ML Predictions: ['SHORT', 'LONG', 'LONG', 'WAIT', 'LONG', 'LONG', 'LONG', 'WAIT']
ML Confidences: ['83.1%', '59.5%', '81.2%', '76.2%', '65.4%', '60.4%', '74.8%', '85.6%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:22:32,066 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:22:36,050 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 545 chars
2025-06-21 09:22:36,050 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 78%
REASONING: The majority of the ML predictions indicate a long position with high confidence in most instances. While there is some variance, and one prediction suggests waiting (with an 85.6% confidence), it aligns more closely with other confident forecasts rather tha...
2025-06-21 09:22:36,051 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 122, Total: 446
2025-06-21 09:22:37,579 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:22:37,579 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:22:37,579 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×150 A:0.16×6316
FLOW: +0% | VOL: +0%
ENSEMBLE: LONG 73%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:22:37,580 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:22:42,607 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 849 chars
2025-06-21 09:22:42,608 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$4.1", // Using $4 for the purchase amount, which is approximately 1% of balance ($41) as per instruction to use a small percentage (0.5-3%) and rounding down due to currency format constraints in JSON representation. The actual quantity would be sligh...
2025-06-21 09:22:42,608 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 354, Completion: 199, Total: 553
2025-06-21 09:23:47,162 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:23:47,163 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:23:47,163 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160729
ML Predictions: ['LONG', 'LONG', 'WAIT', 'LONG', 'WAIT', 'LONG', 'LONG', 'SHORT']
ML Confidences: ['59.7%', '62.5%', '66.6%', '61.9%', '67.0%', '86.1%', '69.5%', '72.1%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:23:47,165 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:23:52,241 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 851 chars
2025-06-21 09:23:52,241 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 74.3%   
REASONING: The majority of Machine Learning (ML) predictions are in favor of a long position with two out of three being 'LONG' and the highest confidence level at 86.1%. Although there is one prediction to wait, which indicates uncertainty or potential for pric...
2025-06-21 09:23:52,242 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 199, Total: 523
2025-06-21 09:23:53,767 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:23:53,767 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:23:53,768 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×6221 A:0.16×1890
FLOW: +0% | VOL: +0%
ENSEMBLE: LONG 68%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:23:53,769 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:23:56,647 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:23:56,649 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:23:56,649 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 355, Completion: 33, Total: 388
2025-06-21 09:25:00,309 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:25:00,310 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:25:00,310 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160636
ML Predictions: ['WAIT', 'WAIT', 'WAIT', 'WAIT', 'WAIT', 'WAIT', 'LONG', 'LONG']
ML Confidences: ['75.9%', '65.2%', '77.6%', '87.7%', '78.6%', '79.5%', '60.5%', '81.1%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:25:00,311 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:25:04,728 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 662 chars
2025-06-21 09:25:04,729 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 82%    
REASONING: The majority of the machine learning model's predictions are 'WAIT', with a significant shift to 'LONG' in recent iterations, indicating an increasing consensus that DOGE/USDT could be undervalued or poised for short-term growth. Additionally, confiden...
2025-06-21 09:25:04,729 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 147, Total: 471
2025-06-21 09:25:06,256 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:25:06,256 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:25:06,257 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×1890 A:0.16×2000
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 76%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:25:06,258 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:25:09,036 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:25:09,036 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:25:09,037 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 33, Total: 389
2025-06-21 09:45:16,348 - main - INFO - Epinnox v6 starting up...
2025-06-21 09:45:16,361 - core.performance_monitor - INFO - Performance monitoring started
2025-06-21 09:45:16,361 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-21 09:45:16,362 - main - INFO - Performance monitoring initialized
2025-06-21 09:45:16,371 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-21 09:45:16,372 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-21 09:45:16,373 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-21 09:45:25,915 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-21 09:45:27,165 - websocket - INFO - Websocket connected
2025-06-21 09:45:31,027 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-21 09:45:31,028 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-21 09:45:31,028 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-21 09:45:31,029 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-21 09:45:31,034 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-21 09:45:33,116 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-21 09:45:33,117 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-21 09:45:33,117 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-21 09:45:33,118 - core.llm_action_executors - INFO - LLM Action Executors initialized
2025-06-21 09:45:33,119 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-21 09:45:33,122 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-21 09:45:33,122 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-21 09:45:33,131 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-21 09:45:33,132 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-21 09:45:33,132 - storage.session_manager - INFO - Session Manager initialized
2025-06-21 09:45:33,141 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250621_094533_2fd415fa
2025-06-21 09:45:33,144 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250621_094533_2fd415fa
2025-06-21 09:45:33,204 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-21 09:52:22,669 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:52:22,670 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:52:22,670 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160150
ML Predictions: ['WAIT', 'LONG', 'SHORT', 'WAIT', 'SHORT', 'WAIT', 'LONG', 'WAIT']
ML Confidences: ['60.9%', '62.8%', '67.3%', '82.6%', '67.8%', '71.0%', '86.6%', '72.3%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:52:22,671 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:52:27,579 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 852 chars
2025-06-21 09:52:27,580 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG   
CONFIDENCE: 86.6%  
REASONING: The ML model's highest confidence level for a 'Long' position is at 86.6%, which strongly suggests that the price will increase in favor of going long on DOGE/USDT. Moreover, considering multiple consecutive predictions with high confidences (71%, 82%...
2025-06-21 09:52:27,580 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 198, Total: 522
2025-06-21 09:52:29,075 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:52:29,075 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:52:29,076 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×1890 A:0.16×6238
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 71%
BAL: $40 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($40) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:52:29,076 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:52:34,000 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 810 chars
2025-06-21 09:52:34,000 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": 4, // Using the calculated quantity of $4 based on a risk percentage of 2% and balance amounting to $160 (balance + position size) after leverage. The order type is market as per instruction requirement for immediate action without specifying limit or s...
2025-06-21 09:52:34,000 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 199, Total: 555
2025-06-21 09:53:36,735 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:53:36,736 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:53:36,736 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160434
ML Predictions: ['LONG', 'LONG', 'LONG', 'WAIT', 'LONG', 'WAIT', 'WAIT', 'WAIT']
ML Confidences: ['57.4%', '81.4%', '72.9%', '76.5%', '77.2%', '85.6%', '68.3%', '67.5%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:53:36,737 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:53:40,748 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 491 chars
2025-06-21 09:53:40,748 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 79%   
REASONING: The ML Predictions show a strong majority (5 out of 8) suggesting 'LONG' actions, with confidence levels ranging from 67.5% to 81.4%. Although there are some indications ('WAIT') that the trend might reverse or be uncertain in certain instances, overall...
2025-06-21 09:53:40,749 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 132, Total: 456
2025-06-21 09:53:42,269 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:53:42,269 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:53:42,270 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×424 A:0.16×7217
FLOW: +0% | VOL: +0%
ENSEMBLE: LONG 73%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:53:42,271 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:53:45,081 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:53:45,082 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:53:45,082 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 354, Completion: 33, Total: 387
2025-06-21 09:54:48,946 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:54:48,947 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:54:48,947 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160323
ML Predictions: ['LONG', 'SHORT', 'LONG', 'WAIT', 'LONG', 'LONG', 'LONG', 'SHORT']
ML Confidences: ['81.8%', '64.9%', '58.8%', '74.5%', '74.6%', '74.4%', '68.1%', '83.4%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:54:48,949 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:54:53,003 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 618 chars
2025-06-21 09:54:53,003 - llama.lmstudio_runner - INFO - 📄 Response Preview: Decision: LONG  
Confidence: 75%   
Reasoning: The majority of the machine learning predictions indicate a 'LONG' trend, with high confidence levels (above 64%). Despite some volatility in ML confidences towards 'SHORT', most signals suggest buying into DOGE/USDT at current prices. A moderate stop l...
2025-06-21 09:54:53,005 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 135, Total: 459
2025-06-21 09:54:54,519 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:54:54,520 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:54:54,520 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×1890 A:0.16×6237
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 73%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:54:54,521 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:54:59,409 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 765 chars
2025-06-21 09:54:59,409 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$4.13 (calculated as $41 * 0.1)", // Using the minimum risk percentage of balance for position sizing, which is typically a conservative approach to manage risks effectively in trading strategies like scalping or day-trading where quick entry and exit ...
2025-06-21 09:54:59,410 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 356, Completion: 199, Total: 555
2025-06-21 09:56:03,404 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:56:03,404 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:56:03,405 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160273
ML Predictions: ['WAIT', 'LONG', 'SHORT', 'SHORT', 'WAIT', 'WAIT', 'WAIT', 'WAIT']
ML Confidences: ['69.3%', '70.0%', '63.0%', '87.3%', '80.2%', '76.7%', '65.2%', '67.2%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:56:03,406 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:56:07,732 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 639 chars
2025-06-21 09:56:07,733 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 75%
REASONING: The current ML predictions are evenly distributed with no clear consensus, and the market conditions appear neutral as per recent trends. While some models suggest a long position could be profitable due to potential price stability or growth (ML Predictions...
2025-06-21 09:56:07,733 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 149, Total: 473
2025-06-21 09:56:09,265 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:56:09,266 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:56:09,266 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.01%)
DEPTH: B:0.16×1700 A:0.16×1890
FLOW: -50% | VOL: -14%
ENSEMBLE: WAIT 72%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:56:09,268 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:56:12,091 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 61 chars
2025-06-21 09:56:12,092 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"ACTION":"BUY","QUANTITY":$41*3%,"LEVERAGE":20,"RISK_PCT":2}...
2025-06-21 09:56:12,093 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 358, Completion: 33, Total: 391
2025-06-21 09:57:15,399 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:57:15,400 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:57:15,400 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160357
ML Predictions: ['WAIT', 'SHORT', 'LONG', 'WAIT', 'WAIT', 'WAIT', 'LONG', 'WAIT']
ML Confidences: ['63.4%', '61.2%', '65.6%', '66.9%', '70.5%', '67.4%', '80.1%', '76.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:57:15,401 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:57:19,813 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 617 chars
2025-06-21 09:57:19,813 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 87%   
REASONING: Given the current price of DOGE/USDT at $0.160357, and considering that among multiple model predictions we have a high confidence 'LONG' recommendation (at 80.1%) with ML Confidences showing an overall tendency towards long positions in this specific p...
2025-06-21 09:57:19,814 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 156, Total: 480
2025-06-21 09:57:21,334 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:57:21,335 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:57:21,335 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×1890 A:0.16×2000
FLOW: -50% | VOL: -14%
ENSEMBLE: WAIT 69%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:57:21,338 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:57:26,355 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 862 chars
2025-06-21 09:57:26,355 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "ACTION": "BUY",
  "QUANTITY": "$4.1", // Using $3 (7% of balance) for position sizing as per instruction to use a small percentage like between 1-3%. Adjusted from the original request due to risk management considerations and not exceeding leverage limits or account size constraints.
 ...
2025-06-21 09:57:26,356 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 358, Completion: 199, Total: 557
2025-06-21 09:58:30,056 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:58:30,057 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-21 09:58:30,058 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.160300
ML Predictions: ['SHORT', 'WAIT', 'WAIT', 'LONG', 'WAIT', 'SHORT', 'LONG', 'SHORT']
ML Confidences: ['63.6%', '55.2%', '68.0%', '62.0%', '88.7%', '62.1%', '89.5%', '71.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-21 09:58:30,059 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-21 09:58:34,711 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 726 chars
2025-06-21 09:58:34,713 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 87.6%
REASONING: The ML predictions show a mix of 'SHORT' and 'LONG', but the majority suggest either waiting or going long, with high confidence in the latter two suggestions (89.5%, 88.7%). Given that there is an overall lean towards buying at this point without signific...
2025-06-21 09:58:34,713 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 324, Completion: 181, Total: 505
2025-06-21 09:58:36,224 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-21 09:58:36,225 - llama.lmstudio_runner - INFO - 📝 System Prompt: AGGRESSIVE SCALPER: NEVER WAIT! BUY or SELL only. WAIT is FORBIDDEN unless market is completely broken. JSON ONLY, NO COMMENTS, NO EXPLANATIONS. IMMEDIATE ACTION REQUIRED....
2025-06-21 09:58:36,226 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP DOGE/USDT:USDT $0.16 | BID/ASK 0.16/0.16 (0.00%)
DEPTH: B:0.16×1890 A:0.16×4754
FLOW: -50% | VOL: -14%
ENSEMBLE: WAIT 70%
BAL: $41 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($41) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":5.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5-5...
2025-06-21 09:58:36,228 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-06-21 09:58:41,180 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 771 chars
2025-06-21 09:58:41,180 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "ACTION": "BUY",
    "QUANTITY": "$41 * (3/100)", // Calculating based on the balance and risk percentage of 3% for position sizing, which equals $1.23 USDT or approximately BTC equivalent if needed to be converted using current exchange rates at time of execution.
    "LEVERAGE": 5, /...
2025-06-21 09:58:41,181 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 358, Completion: 199, Total: 557
