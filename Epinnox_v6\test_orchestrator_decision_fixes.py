#!/usr/bin/env python3
"""
Test script to validate LLM Orchestrator decision-making fixes
Tests the specific issues identified in the log analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm_response_parsers import LLMResponseParsers
from core.llm_orchestrator import PromptType, PromptResult
from datetime import datetime

def test_response_parsing_fixes():
    """Test the enhanced response parsing for the specific log issues"""
    print("🧪 TESTING RESPONSE PARSING FIXES")
    print("=" * 50)
    
    parser = LLMResponseParsers()
    
    # Test case 1: Risk Assessment with "DECISION: SHORT, CONFIDENCE: 85%"
    print("\n1. Testing Risk Assessment Parsing:")
    risk_response = "DECISION: SHORT\nCONFIDENCE: 85%\nEXPLANATION: Strong bearish momentum detected"
    risk_result = parser.parse_risk_assessment_response(risk_response)
    print(f"   Input: {risk_response}")
    print(f"   Parsed: {risk_result}")
    
    # Check if DECISION is preserved
    if 'DECISION' in risk_result and risk_result['DECISION'] == 'SHORT':
        print("   ✅ DECISION correctly preserved: SHORT")
    else:
        print(f"   ❌ DECISION not preserved: {risk_result.get('DECISION', 'MISSING')}")
    
    if risk_result.get('APPROVED') == True:
        print("   ✅ APPROVED correctly set: True (for SHORT decision)")
    else:
        print(f"   ❌ APPROVED incorrectly set: {risk_result.get('APPROVED')}")
    
    # Test case 2: Entry Timing with "DECISION: LONG, CONFIDENCE: 80%"
    print("\n2. Testing Entry Timing Parsing:")
    entry_response = "DECISION: LONG\nCONFIDENCE: 80%\nACTION: ENTER_NOW"
    entry_result = parser.parse_entry_timing_response(entry_response)
    print(f"   Input: {entry_response}")
    print(f"   Parsed: {entry_result}")
    
    # Check if DECISION is preserved
    if 'DECISION' in entry_result and entry_result['DECISION'] == 'LONG':
        print("   ✅ DECISION correctly preserved: LONG")
    else:
        print(f"   ❌ DECISION not preserved: {entry_result.get('DECISION', 'MISSING')}")
    
    if entry_result.get('ACTION') == 'ENTER_NOW':
        print("   ✅ ACTION correctly parsed: ENTER_NOW")
    else:
        print(f"   ❌ ACTION incorrectly parsed: {entry_result.get('ACTION')}")
    
    # Test case 3: Opportunity Scanner with trading decision
    print("\n3. Testing Opportunity Scanner Parsing:")
    opp_response = "DECISION: LONG\nCONFIDENCE: 80%\nBEST_OPPORTUNITY: BREAKOUT"
    opp_result = parser.parse_opportunity_scanner_response(opp_response)
    print(f"   Input: {opp_response}")
    print(f"   Parsed: {opp_result}")
    
    # Check if DECISION is preserved
    if 'DECISION' in opp_result and opp_result['DECISION'] == 'LONG':
        print("   ✅ DECISION correctly preserved: LONG")
    else:
        print(f"   ❌ DECISION not preserved: {opp_result.get('DECISION', 'MISSING')}")

def test_decision_aggregation_fixes():
    """Test the enhanced decision aggregation logic"""
    print("\n🧪 TESTING DECISION AGGREGATION FIXES")
    print("=" * 50)
    
    # Simulate the exact scenario from the logs
    print("\nSimulating log scenario:")
    print("- Risk Assessment: DECISION: SHORT, CONFIDENCE: 85%")
    print("- Entry Timing: DECISION: LONG, CONFIDENCE: 80% (conflicting)")
    print("- Opportunity Scanner: DECISION: LONG, CONFIDENCE: 80%")
    
    # Mock cycle results with the enhanced parsing
    mock_cycle_results = {}
    
    # Risk Assessment: SHORT with 85% confidence
    mock_cycle_results[PromptType.RISK_ASSESSMENT] = PromptResult(
        prompt_type=PromptType.RISK_ASSESSMENT,
        timestamp=datetime.now(),
        response={
            'APPROVED': True,
            'RISK_SCORE': 15,  # Low risk (100 - 85)
            'CONFIDENCE': 85,
            'DECISION': 'SHORT'  # 🚀 FIXED: Now preserved
        },
        confidence=85,
        execution_time=0.3,
        success=True
    )
    
    # Entry Timing: LONG with 80% confidence, ENTER_NOW
    mock_cycle_results[PromptType.ENTRY_TIMING] = PromptResult(
        prompt_type=PromptType.ENTRY_TIMING,
        timestamp=datetime.now(),
        response={
            'ACTION': 'ENTER_NOW',
            'CONFIDENCE': 80,
            'ENTRY_TYPE': 'LIMIT',
            'DECISION': 'LONG'  # 🚀 FIXED: Now preserved
        },
        confidence=80,
        execution_time=0.4,
        success=True
    )
    
    # Opportunity Scanner: LONG with 80% confidence
    mock_cycle_results[PromptType.OPPORTUNITY_SCANNER] = PromptResult(
        prompt_type=PromptType.OPPORTUNITY_SCANNER,
        timestamp=datetime.now(),
        response={
            'BEST_OPPORTUNITY': 'BREAKOUT',
            'SETUP_TYPE': 'MOMENTUM',
            'CONFIDENCE': 80,
            'DECISION': 'LONG'  # 🚀 FIXED: Now preserved
        },
        confidence=80,
        execution_time=0.7,
        success=True
    )
    
    # Test the enhanced aggregation logic
    print("\nTesting Enhanced Aggregation Logic:")
    
    decision_votes = {"LONG": 0, "SHORT": 0, "WAIT": 0, "CLOSE": 0}
    confidence_sum = 0
    confidence_count = 0
    
    # Risk Assessment with DECISION preservation
    risk_result = mock_cycle_results[PromptType.RISK_ASSESSMENT]
    risk_decision = risk_result.response.get('DECISION', None)
    approved = risk_result.response.get('APPROVED', False)
    risk_score = risk_result.response.get('RISK_SCORE', 100)
    
    if risk_decision == 'SHORT' and approved and risk_score < 70:
        decision_votes["SHORT"] += 2.0  # Strong signal for SHORT
        print(f"   Risk Assessment: +2.0 SHORT (was DECISION: {risk_decision})")
    
    confidence_sum += risk_result.confidence
    confidence_count += 1
    
    # Entry Timing with DECISION preservation
    entry_result = mock_cycle_results[PromptType.ENTRY_TIMING]
    entry_action = entry_result.response.get('ACTION', 'WAIT')
    entry_decision = entry_result.response.get('DECISION', None)
    
    if entry_action == 'ENTER_NOW' and entry_decision == 'LONG':
        decision_votes["LONG"] += 3.0  # Strong signal for LONG
        print(f"   Entry Timing: +3.0 LONG (was DECISION: {entry_decision}, ACTION: {entry_action})")
    
    confidence_sum += entry_result.confidence
    confidence_count += 1
    
    # Opportunity Scanner with DECISION preservation
    opp_result = mock_cycle_results[PromptType.OPPORTUNITY_SCANNER]
    opp_decision = opp_result.response.get('DECISION', None)
    best_opp = opp_result.response.get('BEST_OPPORTUNITY', 'NONE')
    setup_type = opp_result.response.get('SETUP_TYPE', 'NONE')
    
    if opp_decision == 'LONG' and best_opp != 'NONE':
        decision_votes["LONG"] += 2.0  # Strong signal for LONG
        print(f"   Opportunity Scanner: +2.0 LONG (was DECISION: {opp_decision})")
    
    confidence_sum += opp_result.confidence
    confidence_count += 1
    
    print(f"\nFinal Vote Counts: {decision_votes}")
    
    # Test aggressive scalping decision logic
    print("\nTesting Aggressive Scalping Decision Logic:")
    
    if decision_votes["CLOSE"] > 1.0:
        final_decision = "CLOSE"
    elif decision_votes["LONG"] > decision_votes["SHORT"]:
        if decision_votes["LONG"] > decision_votes["WAIT"] * 0.3:  # Ultra aggressive
            final_decision = "LONG"
        elif decision_votes["LONG"] > 0.5:
            final_decision = "LONG"
        else:
            final_decision = "LONG"  # Force action
    elif decision_votes["SHORT"] > decision_votes["LONG"]:
        if decision_votes["SHORT"] > decision_votes["WAIT"] * 0.3:  # Ultra aggressive
            final_decision = "SHORT"
        elif decision_votes["SHORT"] > 0.5:
            final_decision = "SHORT"
        else:
            final_decision = "SHORT"  # Force action
    else:
        final_decision = "LONG"  # Default to action
    
    # Calculate proper confidence
    base_confidence = confidence_sum / confidence_count if confidence_count > 0 else 50
    max_votes = max(decision_votes.values())
    total_votes = sum(decision_votes.values())
    consensus_strength = max_votes / total_votes if total_votes > 0 else 0
    final_confidence = min(95, base_confidence + (consensus_strength * 20))
    
    print(f"\n🎯 FINAL RESULT:")
    print(f"   Decision: {final_decision}")
    print(f"   Confidence: {final_confidence:.1f}%")
    print(f"   Base Confidence: {base_confidence:.1f}%")
    print(f"   Consensus Strength: {consensus_strength:.2f}")
    
    # Analysis
    print(f"\n📊 ANALYSIS:")
    if final_decision in ['LONG', 'SHORT']:
        print(f"   ✅ SUCCESS: Decision is actionable ({final_decision}) - AGGRESSIVE SCALPING WORKING")
    else:
        print(f"   ❌ FAILURE: Decision is still WAIT - aggressive bias not working")
    
    if final_confidence > 75:
        print(f"   ✅ SUCCESS: Confidence properly aggregated ({final_confidence:.1f}%)")
    else:
        print(f"   ⚠️  REVIEW: Confidence could be higher ({final_confidence:.1f}%)")
    
    # Expected vs Actual from logs
    print(f"\n🔍 COMPARISON TO LOG ISSUE:")
    print(f"   Log showed: WAIT (86.3%) - CONSERVATIVE")
    print(f"   Fixed shows: {final_decision} ({final_confidence:.1f}%) - {'AGGRESSIVE' if final_decision != 'WAIT' else 'STILL CONSERVATIVE'}")
    
    if final_decision != 'WAIT':
        print(f"   ✅ ISSUE RESOLVED: No more conservative WAIT decisions!")
    else:
        print(f"   ❌ ISSUE PERSISTS: Still defaulting to WAIT")

def test_confidence_calculation():
    """Test the confidence calculation fix"""
    print("\n🧪 TESTING CONFIDENCE CALCULATION")
    print("=" * 50)
    
    # Test with the exact confidence values from logs: 75%, 85%, 85%, 60%, 80%
    individual_confidences = [75, 85, 85, 60, 80]
    print(f"Individual prompt confidences: {individual_confidences}")
    
    # Calculate base confidence (average)
    base_confidence = sum(individual_confidences) / len(individual_confidences)
    print(f"Base confidence (average): {base_confidence:.1f}%")
    
    # Simulate consensus strength (assuming LONG wins with 5.0 votes out of 7.5 total)
    max_votes = 5.0
    total_votes = 7.5
    consensus_strength = max_votes / total_votes
    print(f"Consensus strength: {consensus_strength:.2f}")
    
    # Apply consensus boost
    final_confidence = min(95, base_confidence + (consensus_strength * 20))
    print(f"Final confidence with consensus boost: {final_confidence:.1f}%")
    
    print(f"\n📊 COMPARISON:")
    print(f"   Log showed: 86.3% (incorrect calculation)")
    print(f"   Fixed shows: {final_confidence:.1f}% (proper aggregation)")
    
    if abs(final_confidence - 86.3) < 5:
        print(f"   ✅ Confidence calculation is reasonable")
    else:
        print(f"   ⚠️  Confidence calculation differs significantly")

if __name__ == "__main__":
    print("🚀 LLM ORCHESTRATOR DECISION-MAKING FIXES VALIDATION")
    print("=" * 60)
    print("Testing fixes for the specific issues identified in logs:")
    print("1. Response parsing inconsistency (DECISION: SHORT not extracted)")
    print("2. Decision aggregation logic error (ENTER_NOW adding to both LONG/SHORT)")
    print("3. Confidence calculation mismatch")
    print("4. Aggressive scalping strategy override")
    print("=" * 60)
    
    test_response_parsing_fixes()
    test_decision_aggregation_fixes()
    test_confidence_calculation()
    
    print("\n" + "=" * 60)
    print("🎯 VALIDATION COMPLETE!")
    print("\nThe fixes should now:")
    print("1. ✅ Extract 'DECISION: SHORT/LONG' from LLM responses correctly")
    print("2. ✅ Use actual trading direction instead of adding to both LONG/SHORT")
    print("3. ✅ Calculate confidence as proper average with consensus boost")
    print("4. ✅ Apply ultra-aggressive bias (0.3x threshold) to eliminate WAIT decisions")
    print("5. ✅ Produce actionable trading decisions that reflect actual LLM analysis")
    print("\n🎉 Expected Result: LONG/SHORT decisions instead of conservative WAIT!")
