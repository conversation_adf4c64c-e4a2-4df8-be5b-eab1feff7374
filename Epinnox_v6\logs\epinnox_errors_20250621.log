2025-06-21 12:04:33,108 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x00000159EFB03EE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 12:04:33,110 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x00000159EFB03EE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 17:15:36,155 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:38,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:39,304 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:40,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:41,311 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:42,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:43,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:44,298 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:45,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:46,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:47,535 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:48,302 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:49,671 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:50,299 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:51,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:52,296 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:53,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:54,298 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:55,305 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:56,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:57,530 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:58,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:59,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:00,302 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:01,304 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:02,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:03,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:04,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:05,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:06,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:07,515 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:08,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:09,700 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:10,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:11,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:12,310 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:13,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:14,312 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:16,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:18,034 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:18,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:19,675 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:20,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:21,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:22,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:23,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:24,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:25,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:26,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:27,854 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:28,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:29,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:30,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:31,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:32,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:33,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:34,312 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:35,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:36,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:37,965 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:56,325 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:58,321 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:59,679 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:00,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:01,319 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:02,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:03,325 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:05,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:06,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:07,524 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:08,317 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:09,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:10,319 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:11,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:12,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:13,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:14,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:15,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:18,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:19,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:20,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:21,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:22,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:23,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:24,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:25,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:26,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:27,496 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:28,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:29,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:30,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:31,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:32,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:33,327 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:34,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:35,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:36,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:37,511 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:38,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:39,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:40,327 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:41,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:42,266 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x000001E8B1D32160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 17:17:42,267 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x000001E8B1D32160>>: wrapped C/C++ object of type WebSocketClient has been deleted
