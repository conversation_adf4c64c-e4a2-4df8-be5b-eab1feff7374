#!/usr/bin/env python3
"""
Test script to verify the Dynamic Symbol Scanner GUI integration
Tests the scanner checkbox and timer functionality
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QCheckBox, QLabel, QPushButton
from PyQt5.QtCore import QTimer, Qt
from unittest.mock import Mock

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from symbol_scanner import SymbolScanner, SymbolScannerConfig


class MockExchange:
    """Mock exchange for GUI testing"""
    
    def __init__(self):
        self.symbols_data = {
            'BTC/USDT:USDT': {'price': 45000.0, 'volume': 1000000000, 'score': 85.5},
            'ETH/USDT:USDT': {'price': 3200.0, 'volume': 500000000, 'score': 72.3},
            'DOGE/USDT:USDT': {'price': 0.08, 'volume': 100000000, 'score': 68.1},
            'ADA/USDT:USDT': {'price': 0.45, 'volume': 80000000, 'score': 55.7},
        }
        self.current_best = 'BTC/USDT:USDT'
        self.call_count = 0
    
    def load_markets(self):
        return {symbol: {'active': True} for symbol in self.symbols_data.keys()}
    
    def fetch_ticker(self, symbol):
        data = self.symbols_data.get(symbol, {'price': 100.0, 'volume': 1000000})
        return {'last': data['price'], 'quoteVolume': data['volume']}
    
    def fetch_order_book(self, symbol, limit=5):
        price = self.symbols_data.get(symbol, {'price': 100.0})['price']
        spread = price * 0.001
        return {
            'bids': [[price - spread, 100.0]] * limit,
            'asks': [[price + spread, 100.0]] * limit
        }
    
    def fetch_trades(self, symbol, limit=50):
        price = self.symbols_data.get(symbol, {'price': 100.0})['price']
        return [{'price': price, 'amount': 1.0, 'side': 'buy'}] * min(limit, 10)
    
    def simulate_market_change(self):
        """Simulate market conditions changing"""
        self.call_count += 1
        
        # Every 3 calls, change the best symbol
        if self.call_count % 3 == 0:
            symbols = list(self.symbols_data.keys())
            current_index = symbols.index(self.current_best)
            next_index = (current_index + 1) % len(symbols)
            self.current_best = symbols[next_index]
            
            # Boost the score of the new best symbol
            for symbol in symbols:
                if symbol == self.current_best:
                    self.symbols_data[symbol]['score'] = 90.0 + (self.call_count % 10)
                else:
                    self.symbols_data[symbol]['score'] = 50.0 + (hash(symbol) % 30)


class ScannerTestWidget(QWidget):
    """Test widget for scanner GUI functionality"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_scanner()
        
    def init_ui(self):
        """Initialize the test UI"""
        self.setWindowTitle("Dynamic Symbol Scanner Test")
        self.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("Dynamic Symbol Scanner GUI Test")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #00ff00;")
        layout.addWidget(title)
        
        # Scanner checkbox (mimics the real GUI)
        self.dynamic_scan_cb = QCheckBox("🤖 Auto-Select Best Symbol")
        self.dynamic_scan_cb.setToolTip("Automatically scan and select the best trading symbol")
        self.dynamic_scan_cb.stateChanged.connect(self.on_dynamic_scan_toggled)
        layout.addWidget(self.dynamic_scan_cb)
        
        # Scanner status label (mimics the real GUI)
        self.scanner_status_label = QLabel("Scanner: Disabled")
        self.scanner_status_label.setStyleSheet("color: #ffff00; font-size: 12px; padding: 2px;")
        layout.addWidget(self.scanner_status_label)
        
        # Current symbol display
        self.current_symbol_label = QLabel("Current Symbol: DOGE/USDT:USDT")
        self.current_symbol_label.setStyleSheet("color: #ffffff; font-size: 14px;")
        layout.addWidget(self.current_symbol_label)
        
        # Scanner metrics display
        self.metrics_label = QLabel("Metrics: Not scanning")
        self.metrics_label.setStyleSheet("color: #cccccc; font-size: 12px;")
        layout.addWidget(self.metrics_label)
        
        # Test controls
        self.simulate_button = QPushButton("Simulate Market Change")
        self.simulate_button.clicked.connect(self.simulate_market_change)
        layout.addWidget(self.simulate_button)
        
        # Log display
        self.log_label = QLabel("Log: Ready for testing")
        self.log_label.setStyleSheet("color: #aaaaaa; font-size: 10px;")
        self.log_label.setWordWrap(True)
        layout.addWidget(self.log_label)
        
        self.setLayout(layout)
        
        # Apply dark theme
        self.setStyleSheet("""
            QWidget {
                background-color: #000000;
                color: #00ff00;
            }
            QCheckBox {
                color: #00ff00;
                font-size: 14px;
            }
            QPushButton {
                background-color: #003300;
                color: #00ff00;
                border: 1px solid #00ff00;
                padding: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #006600;
            }
        """)
    
    def init_scanner(self):
        """Initialize the symbol scanner"""
        self.mock_exchange = MockExchange()
        self.symbol_scanner = SymbolScanner(
            market_api=self.mock_exchange,
            symbols=list(self.mock_exchange.symbols_data.keys()),
            metrics_weights=SymbolScannerConfig.DEFAULT_WEIGHTS
        )
        
        # Scanner state
        self.scanner_enabled = False
        self.current_symbol = 'DOGE/USDT:USDT'
        
        # Scanner timer (mimics the real GUI timer)
        self.scanner_timer = QTimer()
        self.scanner_timer.timeout.connect(self.on_scan_tick)
        self.scanner_timer.setSingleShot(False)
        
        self.log_message("✅ Scanner initialized with test data")
    
    def on_dynamic_scan_toggled(self, state):
        """Handle dynamic scanner checkbox toggle (mimics real GUI)"""
        try:
            if state == Qt.Checked:
                # Enable dynamic scanning
                self.scanner_enabled = True
                self.scanner_timer.start(3000)  # Scan every 3 seconds for testing
                self.scanner_status_label.setText("Scanner: Active")
                self.scanner_status_label.setStyleSheet("color: #00ff00; font-size: 12px; font-weight: bold; padding: 2px;")
                self.log_message("🤖 Dynamic symbol scanner enabled")
                
                # Perform initial scan
                QTimer.singleShot(500, self.on_scan_tick)
                
            else:
                # Disable dynamic scanning
                self.scanner_enabled = False
                self.scanner_timer.stop()
                self.scanner_status_label.setText("Scanner: Disabled")
                self.scanner_status_label.setStyleSheet("color: #ffff00; font-size: 12px; padding: 2px;")
                self.log_message("🤖 Dynamic symbol scanner disabled")
                
        except Exception as e:
            self.log_message(f"❌ Error toggling scanner: {e}")
    
    def on_scan_tick(self):
        """Handle scanner timer tick (mimics real GUI)"""
        try:
            if not self.scanner_enabled:
                return
            
            # Find best symbol
            best_symbols = self.symbol_scanner.find_best(n=1)
            
            if best_symbols:
                best_symbol = best_symbols[0]
                
                # Update symbol if it changed
                if best_symbol != self.current_symbol:
                    self.log_message(f"🎯 Scanner found better symbol: {best_symbol} (was: {self.current_symbol})")
                    self.current_symbol = best_symbol
                    self.current_symbol_label.setText(f"Current Symbol: {best_symbol}")
                    self.current_symbol_label.setStyleSheet("color: #00ff00; font-size: 14px; font-weight: bold;")
                else:
                    self.current_symbol_label.setStyleSheet("color: #ffffff; font-size: 14px;")
                
                # Update status with current symbol score
                metrics = self.symbol_scanner.get_symbol_metrics(best_symbol)
                if metrics:
                    self.scanner_status_label.setText(f"Scanner: {best_symbol} ({metrics.score:.1f})")
                    self.metrics_label.setText(f"Metrics: Spread {metrics.spread_pct:.3f}%, ATR {metrics.tick_atr:.6f}, Depth {metrics.orderbook_depth:.0f}")
                else:
                    self.scanner_status_label.setText(f"Scanner: {best_symbol}")
                    self.metrics_label.setText("Metrics: No data available")
            else:
                self.log_message("⚠️ Scanner found no suitable symbols")
                
        except Exception as e:
            self.log_message(f"❌ Error in scanner tick: {e}")
    
    def simulate_market_change(self):
        """Simulate market conditions changing"""
        self.mock_exchange.simulate_market_change()
        self.log_message(f"📊 Simulated market change (call #{self.mock_exchange.call_count})")
        
        # If scanner is active, it will pick up the change on next tick
        if self.scanner_enabled:
            self.log_message("⏱️ Scanner will detect changes on next tick...")
    
    def log_message(self, message):
        """Log message to the display"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}"
        self.log_label.setText(log_text)
        print(log_text)  # Also print to console


def main():
    """Run the scanner GUI test"""
    print("🧪 DYNAMIC SYMBOL SCANNER GUI TEST")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create test widget
    test_widget = ScannerTestWidget()
    test_widget.show()
    
    print("✅ Test GUI launched")
    print("📋 Test Instructions:")
    print("   1. Check the '🤖 Auto-Select Best Symbol' checkbox")
    print("   2. Watch the scanner status and current symbol")
    print("   3. Click 'Simulate Market Change' to test symbol switching")
    print("   4. Uncheck the checkbox to disable scanning")
    print("   5. Close the window when done testing")
    
    # Run the application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
