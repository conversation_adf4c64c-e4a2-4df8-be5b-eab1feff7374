"""
LLM Action Executors - Execute trading actions based on LLM decisions
Handles position management, emergency responses, and trade execution
"""

import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class LLMActionExecutors:
    """
    Execute trading actions based on LLM prompt responses
    Handles all types of trading decisions with proper risk management
    """
    
    def __init__(self, trading_interface, main_window):
        self.trading_interface = trading_interface
        self.main_window = main_window
        self.execution_log = []
        
        # Safety limits
        self.max_daily_trades = 50
        self.max_concurrent_positions = 3
        self.max_daily_loss = 100.0  # USD
        
        # Execution tracking
        self.daily_trades = 0
        self.daily_loss = 0.0
        self.last_trade_time = 0
        
        logger.info("LLM Action Executors initialized")
    
    def execute_emergency_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute emergency response actions"""
        try:
            action = response.get('ACTION', 'MONITOR')
            priority = response.get('PRIORITY', 'MEDIUM')
            
            logger.warning(f"Executing emergency action: {action} (Priority: {priority})")
            
            if action == 'CLOSE_ALL_POSITIONS':
                return self.close_all_positions_emergency()
            
            elif action == 'CLOSE_LOSING':
                return self.close_losing_positions()
            
            elif action == 'HEDGE':
                return self.create_hedge_positions(context)
            
            elif action == 'REDUCE_SIZE':
                return self.reduce_position_sizes()
            
            elif action == 'MONITOR':
                logger.info("Emergency monitoring - no immediate action required")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error executing emergency actions: {e}")
            return False
    
    def execute_position_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute position management actions"""
        try:
            action = response.get('ACTION', 'HOLD')
            urgency = response.get('URGENCY', 'LOW')
            
            logger.info(f"Executing position action: {action} (Urgency: {urgency})")
            
            if action == 'CLOSE':
                return self.close_position_full(context)
            
            elif action == 'PARTIAL_CLOSE':
                close_percentage = response.get('CLOSE_PERCENTAGE', 50)
                return self.close_position_partial(context, close_percentage)
            
            elif action == 'HOLD':
                # Update stop loss and take profit if provided
                return self.update_position_levels(response, context)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing position actions: {e}")
            return False
    
    def execute_profit_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute profit optimization actions"""
        try:
            action = response.get('ACTION', 'HOLD')
            
            logger.info(f"Executing profit action: {action}")
            
            if action == 'FULL_CLOSE':
                return self.close_position_full(context)
            
            elif action == 'PARTIAL_CLOSE':
                close_percentage = response.get('CLOSE_PERCENTAGE', 50)
                trail_stop = response.get('TRAIL_STOP', False)
                
                success = self.close_position_partial(context, close_percentage)
                
                if success and trail_stop:
                    self.set_trailing_stop(response, context)
                
                return success
            
            elif action == 'HOLD':
                # Set trailing stop if requested
                if response.get('TRAIL_STOP', False):
                    return self.set_trailing_stop(response, context)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing profit actions: {e}")
            return False
    
    def execute_entry_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute entry timing actions"""
        try:
            action = response.get('ACTION', 'WAIT')
            entry_type = response.get('ENTRY_TYPE', 'LIMIT')
            
            if action != 'ENTER_NOW':
                return True
            
            logger.info(f"Executing entry action: {action} ({entry_type} order)")
            
            # Check if we have capacity for new positions
            if not self.has_position_capacity():
                logger.warning("No position capacity available - skipping entry")
                return False
            
            # Check daily trade limits
            if not self.check_daily_limits():
                logger.warning("Daily trade limits exceeded - skipping entry")
                return False
            
            # Get trade parameters from context
            trade_params = self.build_trade_parameters(response, context)
            
            if entry_type == 'MARKET':
                return self.execute_market_entry(trade_params, context)
            else:
                return self.execute_limit_entry(trade_params, context)
            
        except Exception as e:
            logger.error(f"Error executing entry actions: {e}")
            return False
    
    def apply_strategy_adaptations(self, response: Dict[str, Any], context) -> bool:
        """Apply strategy adaptation changes"""
        try:
            risk_adjustment = response.get('RISK_ADJUSTMENT', 1.0)
            hold_time_target = response.get('HOLD_TIME_TARGET', 8)
            entry_threshold = response.get('ENTRY_THRESHOLD', 70)
            exit_threshold = response.get('EXIT_THRESHOLD', 60)
            
            logger.info(f"Applying strategy adaptations: Risk {risk_adjustment:.1f}x, Hold time {hold_time_target}min")
            
            # Update strategy parameters in main window
            if hasattr(self.main_window, 'update_strategy_parameters'):
                self.main_window.update_strategy_parameters({
                    'risk_multiplier': risk_adjustment,
                    'target_hold_time': hold_time_target,
                    'entry_confidence_threshold': entry_threshold,
                    'exit_confidence_threshold': exit_threshold
                })
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying strategy adaptations: {e}")
            return False
    
    def close_all_positions_emergency(self) -> bool:
        """Close all positions immediately using market orders"""
        try:
            if not hasattr(self.trading_interface, 'real_trading'):
                return False
            
            positions = self.trading_interface.real_trading.get_open_positions()
            
            if not positions:
                logger.info("No positions to close")
                return True
            
            logger.warning(f"EMERGENCY: Closing {len(positions)} positions")
            
            success_count = 0
            for position in positions:
                try:
                    symbol = position.get('symbol', '')
                    side = position.get('side', '')
                    size = position.get('size', 0)
                    
                    # Determine close side (opposite of position side)
                    close_side = 'sell' if side.lower() == 'long' else 'buy'
                    
                    # Execute market close order
                    result = self.trading_interface.real_trading.place_order(
                        symbol=symbol,
                        side=close_side,
                        amount=size,
                        order_type='market',
                        reduce_only=True
                    )
                    
                    if result and result.get('success'):
                        success_count += 1
                        logger.info(f"Emergency closed {symbol} {side} position")
                    else:
                        logger.error(f"Failed to close {symbol} position: {result}")
                        
                except Exception as e:
                    logger.error(f"Error closing individual position: {e}")
            
            logger.warning(f"Emergency close completed: {success_count}/{len(positions)} positions closed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error in emergency close all: {e}")
            return False
    
    def close_losing_positions(self) -> bool:
        """Close only losing positions"""
        try:
            if not hasattr(self.trading_interface, 'real_trading'):
                return False
            
            positions = self.trading_interface.real_trading.get_open_positions()
            losing_positions = [pos for pos in positions if pos.get('unrealized_pnl', 0) < 0]
            
            if not losing_positions:
                logger.info("No losing positions to close")
                return True
            
            logger.warning(f"Closing {len(losing_positions)} losing positions")
            
            success_count = 0
            for position in losing_positions:
                if self.close_single_position(position):
                    success_count += 1
            
            logger.info(f"Closed {success_count}/{len(losing_positions)} losing positions")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error closing losing positions: {e}")
            return False
    
    def close_position_full(self, context) -> bool:
        """Close a position completely"""
        try:
            # Get the most critical position from context
            if not context.open_positions:
                return True
            
            position = context.open_positions[0]  # Assume first position is target
            return self.close_single_position(position)
            
        except Exception as e:
            logger.error(f"Error closing position full: {e}")
            return False
    
    def close_position_partial(self, context, close_percentage: float) -> bool:
        """Close a percentage of a position"""
        try:
            if not context.open_positions:
                return True
            
            position = context.open_positions[0]
            original_size = position.get('size', 0)
            close_size = original_size * (close_percentage / 100)
            
            if close_size <= 0:
                return True
            
            symbol = position.get('symbol', '')
            side = position.get('side', '')
            close_side = 'sell' if side.lower() == 'long' else 'buy'
            
            logger.info(f"Partial close: {close_percentage}% of {symbol} position ({close_size:.4f})")
            
            result = self.trading_interface.real_trading.place_order(
                symbol=symbol,
                side=close_side,
                amount=close_size,
                order_type='market',
                reduce_only=True
            )
            
            if result and result.get('success'):
                logger.info(f"Partial close successful: {close_percentage}% of {symbol}")
                return True
            else:
                logger.error(f"Partial close failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error in partial close: {e}")
            return False
    
    def close_single_position(self, position: Dict[str, Any]) -> bool:
        """Close a single position"""
        try:
            symbol = position.get('symbol', '')
            side = position.get('side', '')
            size = position.get('size', 0)
            
            close_side = 'sell' if side.lower() == 'long' else 'buy'
            
            result = self.trading_interface.real_trading.place_order(
                symbol=symbol,
                side=close_side,
                amount=size,
                order_type='market',
                reduce_only=True
            )
            
            if result and result.get('success'):
                logger.info(f"Closed {symbol} {side} position")
                return True
            else:
                logger.error(f"Failed to close {symbol} position: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error closing single position: {e}")
            return False
    
    def has_position_capacity(self) -> bool:
        """Check if we have capacity for new positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading'):
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) < self.max_concurrent_positions
        except:
            pass
        return False
    
    def check_daily_limits(self) -> bool:
        """Check if daily trading limits are exceeded"""
        # Reset daily counters if new day
        current_time = time.time()
        if current_time - self.last_trade_time > 86400:  # 24 hours
            self.daily_trades = 0
            self.daily_loss = 0.0
        
        # Check limits
        if self.daily_trades >= self.max_daily_trades:
            return False
        
        if self.daily_loss >= self.max_daily_loss:
            return False
        
        return True
    
    def build_trade_parameters(self, response: Dict[str, Any], context) -> Dict[str, Any]:
        """Build trade parameters from LLM response and context"""
        return {
            'symbol': context.symbol,
            'side': 'buy',  # Default, should be determined by signal
            'quantity': 10.0,  # Default, should be calculated
            'entry_type': response.get('ENTRY_TYPE', 'LIMIT'),
            'confidence': response.get('CONFIDENCE', 50)
        }
    
    def execute_market_entry(self, trade_params: Dict[str, Any], context) -> bool:
        """Execute market entry order"""
        try:
            # Implementation would place market order
            logger.info(f"Market entry: {trade_params['symbol']} {trade_params['side']} {trade_params['quantity']}")
            self.daily_trades += 1
            self.last_trade_time = time.time()
            return True
        except Exception as e:
            logger.error(f"Error in market entry: {e}")
            return False
    
    def execute_limit_entry(self, trade_params: Dict[str, Any], context) -> bool:
        """Execute limit entry order"""
        try:
            # Implementation would place limit order
            logger.info(f"Limit entry: {trade_params['symbol']} {trade_params['side']} {trade_params['quantity']}")
            self.daily_trades += 1
            self.last_trade_time = time.time()
            return True
        except Exception as e:
            logger.error(f"Error in limit entry: {e}")
            return False
