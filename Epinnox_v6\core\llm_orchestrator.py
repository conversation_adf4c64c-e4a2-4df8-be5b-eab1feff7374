"""
LLM Prompt Orchestrator - Complete AI Trading System
Manages multiple specialized LLM prompts for comprehensive trading decisions
"""

import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class PromptPriority(Enum):
    EMERGENCY = 1
    CRITICAL = 2
    HIGH = 3
    MEDIUM = 4
    LOW = 5

class PromptType(Enum):
    EMERGENCY_RESPONSE = "emergency_response"
    POSITION_MANAGEMENT = "position_management"
    PROFIT_OPTIMIZATION = "profit_optimization"
    MARKET_REGIME = "market_regime"
    RISK_ASSESSMENT = "risk_assessment"
    ENTRY_TIMING = "entry_timing"
    STRATEGY_ADAPTATION = "strategy_adaptation"
    OPPORTUNITY_SCANNER = "opportunity_scanner"

@dataclass
class PromptResult:
    prompt_type: PromptType
    timestamp: datetime
    response: Dict[str, Any]
    confidence: float
    execution_time: float
    success: bool
    error_message: Optional[str] = None

@dataclass
class TradingContext:
    """Complete trading context for LLM prompts"""
    symbol: str
    current_price: float
    account_balance: float
    open_positions: List[Dict]
    market_data: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    emergency_flags: List[str]
    timestamp: datetime

class LLMPromptOrchestrator:
    """
    Master orchestrator for all LLM trading prompts
    Manages priority, timing, and execution of specialized AI trading decisions
    """
    
    def should_execute_prompt(self, prompt_type: PromptType) -> bool:
        """Determine if a prompt should be executed based on timing and conditions"""
        
        # Check timing interval
        last_execution = self.last_execution_times.get(prompt_type)
        if last_execution:
            time_since_last = time.time() - last_execution
            if time_since_last < self.prompt_intervals[prompt_type]:
                return False
        
        # Emergency prompts always execute if emergency conditions exist
        if prompt_type == PromptType.EMERGENCY_RESPONSE:
            return len(self.emergency_flags) > 0
        
        # Position-related prompts only if positions exist
        if prompt_type in [PromptType.POSITION_MANAGEMENT, PromptType.PROFIT_OPTIMIZATION]:
            return self.has_active_positions()
        
        # Market analysis prompts only if not in emergency mode
        if prompt_type in [PromptType.MARKET_REGIME, PromptType.OPPORTUNITY_SCANNER]:
            return not self.emergency_mode
        
        # Entry timing only if we have capacity for new positions
        if prompt_type == PromptType.ENTRY_TIMING:
            return self.has_position_capacity() and not self.emergency_mode
        
        return True
    
    def execute_prompt_cycle(self, trading_context: TradingContext) -> Dict[str, PromptResult]:
        """Execute a complete cycle of LLM prompts based on priority and conditions"""
        
        cycle_results = {}
        start_time = time.time()
        
        try:
            # Update emergency flags
            self.update_emergency_flags(trading_context)
            
            # Priority 1: Emergency Response (IMMEDIATE)
            if self.should_execute_prompt(PromptType.EMERGENCY_RESPONSE):
                result = self.execute_emergency_response(trading_context)
                cycle_results[PromptType.EMERGENCY_RESPONSE] = result
                
                # If emergency action taken, skip other prompts
                if result.success and result.response.get('ACTION') != 'MONITOR':
                    logger.warning("Emergency action taken - skipping other prompts")
                    return cycle_results
            
            # Priority 2: Position Management (CRITICAL)
            if self.should_execute_prompt(PromptType.POSITION_MANAGEMENT):
                result = self.execute_position_management(trading_context)
                cycle_results[PromptType.POSITION_MANAGEMENT] = result
            
            # Priority 3: Profit Optimization (HIGH)
            if self.should_execute_prompt(PromptType.PROFIT_OPTIMIZATION):
                result = self.execute_profit_optimization(trading_context)
                cycle_results[PromptType.PROFIT_OPTIMIZATION] = result
            
            # Priority 4: Market Regime Detection (MEDIUM)
            if self.should_execute_prompt(PromptType.MARKET_REGIME):
                result = self.execute_market_regime_detection(trading_context)
                cycle_results[PromptType.MARKET_REGIME] = result
                
                # Update market regime state
                if result.success:
                    self.market_regime = result.response.get('REGIME', 'UNKNOWN')
            
            # Priority 5: Risk Assessment (MEDIUM)
            if self.should_execute_prompt(PromptType.RISK_ASSESSMENT):
                result = self.execute_risk_assessment(trading_context)
                cycle_results[PromptType.RISK_ASSESSMENT] = result
            
            # Priority 6: Entry Timing (MEDIUM)
            if self.should_execute_prompt(PromptType.ENTRY_TIMING):
                result = self.execute_entry_timing(trading_context)
                cycle_results[PromptType.ENTRY_TIMING] = result
            
            # Priority 7: Strategy Adaptation (LOW)
            if self.should_execute_prompt(PromptType.STRATEGY_ADAPTATION):
                result = self.execute_strategy_adaptation(trading_context)
                cycle_results[PromptType.STRATEGY_ADAPTATION] = result
            
            # Priority 8: Opportunity Scanner (LOW)
            if self.should_execute_prompt(PromptType.OPPORTUNITY_SCANNER):
                result = self.execute_opportunity_scanner(trading_context)
                cycle_results[PromptType.OPPORTUNITY_SCANNER] = result
            
            # Update execution times
            for prompt_type in cycle_results.keys():
                self.last_execution_times[prompt_type] = start_time
            
            # Log cycle completion
            cycle_time = time.time() - start_time
            logger.info(f"LLM prompt cycle completed in {cycle_time:.2f}s - {len(cycle_results)} prompts executed")
            
            return cycle_results
            
        except Exception as e:
            logger.error(f"Error in LLM prompt cycle: {e}")
            return cycle_results
    
    def has_active_positions(self) -> bool:
        """Check if there are active positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) > 0
        except:
            pass
        return False
    
    def has_position_capacity(self) -> bool:
        """Check if we have capacity for new positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) < 3  # Max 3 concurrent positions
        except:
            pass
        return True
    
    def update_emergency_flags(self, trading_context: TradingContext):
        """Update emergency flags based on current conditions"""
        self.emergency_flags.clear()
        
        try:
            # Check for flash crash (>2% price drop in 1 minute)
            if self.detect_flash_crash(trading_context):
                self.emergency_flags.append("FLASH_CRASH")
            
            # Check for liquidity crisis (spread >1%)
            if self.detect_liquidity_crisis(trading_context):
                self.emergency_flags.append("LIQUIDITY_CRISIS")
            
            # Check for margin risk (account risk >80%)
            if self.detect_margin_risk(trading_context):
                self.emergency_flags.append("MARGIN_RISK")
            
            # Check for unusual volume spike
            if self.detect_volume_spike(trading_context):
                self.emergency_flags.append("VOLUME_SPIKE")
            
            # Update emergency mode
            self.emergency_mode = len(self.emergency_flags) > 0
            
        except Exception as e:
            logger.error(f"Error updating emergency flags: {e}")
    
    def detect_flash_crash(self, context: TradingContext) -> bool:
        """Detect flash crash conditions"""
        # Implementation would check recent price movements
        return False
    
    def detect_liquidity_crisis(self, context: TradingContext) -> bool:
        """Detect liquidity crisis conditions"""
        # Implementation would check bid-ask spreads
        return False
    
    def detect_margin_risk(self, context: TradingContext) -> bool:
        """Detect margin risk conditions"""
        # Implementation would check account risk levels
        return False
    
    def detect_volume_spike(self, context: TradingContext) -> bool:
        """Detect unusual volume spikes"""
        # Implementation would check volume patterns
        return False

    def execute_emergency_response(self, context: TradingContext) -> PromptResult:
        """Execute emergency response prompt"""
        start_time = time.time()

        try:
            prompt = self.build_emergency_response_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_emergency_response(response_text)

            execution_time = time.time() - start_time

            # Execute emergency actions if needed
            if response.get('ACTION') in ['CLOSE_ALL_POSITIONS', 'CLOSE_LOSING']:
                self.execute_emergency_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in emergency response: {e}")
            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_position_management(self, context: TradingContext) -> PromptResult:
        """Execute position management prompt"""
        start_time = time.time()

        try:
            prompt = self.build_position_management_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_position_management_response(response_text)

            execution_time = time.time() - start_time

            # Execute position management actions
            if response.get('ACTION') != 'HOLD':
                self.execute_position_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in position management: {e}")
            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_profit_optimization(self, context: TradingContext) -> PromptResult:
        """Execute profit optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_profit_optimization_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_profit_optimization_response(response_text)

            execution_time = time.time() - start_time

            # Execute profit optimization actions
            if response.get('ACTION') in ['PARTIAL_CLOSE', 'FULL_CLOSE']:
                self.execute_profit_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in profit optimization: {e}")
            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_market_regime_detection(self, context: TradingContext) -> PromptResult:
        """Execute market regime detection prompt"""
        start_time = time.time()

        try:
            prompt = self.build_market_regime_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_market_regime_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in market regime detection: {e}")
            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_risk_assessment(self, context: TradingContext) -> PromptResult:
        """Execute risk assessment prompt"""
        start_time = time.time()

        try:
            prompt = self.build_risk_assessment_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_risk_assessment_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_entry_timing(self, context: TradingContext) -> PromptResult:
        """Execute entry timing optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_entry_timing_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_entry_timing_response(response_text)

            execution_time = time.time() - start_time

            # Execute entry timing actions if immediate entry recommended
            if response.get('ACTION') == 'ENTER_NOW':
                self.execute_entry_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in entry timing: {e}")
            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_strategy_adaptation(self, context: TradingContext) -> PromptResult:
        """Execute strategy adaptation prompt"""
        start_time = time.time()

        try:
            prompt = self.build_strategy_adaptation_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_strategy_adaptation_response(response_text)

            execution_time = time.time() - start_time

            # Apply strategy adaptations
            self.apply_strategy_adaptations(response, context)

            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in strategy adaptation: {e}")
            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_opportunity_scanner(self, context: TradingContext) -> PromptResult:
        """Execute opportunity scanner prompt"""
        start_time = time.time()

        try:
            prompt = self.build_opportunity_scanner_prompt(context)
            response_text = self.lmstudio_runner.generate_response(prompt)
            response = self.parse_opportunity_scanner_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in opportunity scanner: {e}")
            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    # Import the builders and parsers
    def __init__(self, lmstudio_runner, main_window):
        from .llm_prompt_builders import LLMPromptBuilders
        from .llm_response_parsers import LLMResponseParsers
        from .llm_action_executors import LLMActionExecutors

        self.lmstudio_runner = lmstudio_runner
        self.main_window = main_window  # Main window has all trading methods
        self.trading_interface = main_window  # For backward compatibility

        # Initialize components
        self.prompt_builders = LLMPromptBuilders(main_window)
        self.response_parsers = LLMResponseParsers()
        self.action_executors = LLMActionExecutors(main_window, main_window)

        # Prompt execution tracking
        self.prompt_results = {}
        self.last_execution_times = {}
        self.prompt_queue = []

        # Emergency and state management
        self.emergency_mode = False
        self.emergency_flags = []
        self.market_regime = "UNKNOWN"
        self.strategy_state = "NORMAL"

        # Performance tracking
        self.prompt_performance = {}
        self.execution_stats = {}

        # Configuration
        self.prompt_intervals = {
            PromptType.EMERGENCY_RESPONSE: 5,      # 5 seconds
            PromptType.POSITION_MANAGEMENT: 10,    # 10 seconds
            PromptType.PROFIT_OPTIMIZATION: 15,    # 15 seconds
            PromptType.MARKET_REGIME: 30,          # 30 seconds
            PromptType.RISK_ASSESSMENT: 45,        # 45 seconds
            PromptType.ENTRY_TIMING: 20,           # 20 seconds
            PromptType.STRATEGY_ADAPTATION: 120,   # 2 minutes
            PromptType.OPPORTUNITY_SCANNER: 60,    # 1 minute
        }

        logger.info("LLM Prompt Orchestrator initialized")

    def set_main_window(self, main_window):
        """Set main window reference for action executors (already set in __init__)"""
        # Main window is already set in __init__, but keep this method for compatibility
        self.main_window = main_window
        self.action_executors.main_window = main_window

    # Add prompt building methods
    def build_emergency_response_prompt(self, context):
        return self.prompt_builders.build_emergency_response_prompt(context)

    def build_position_management_prompt(self, context):
        return self.prompt_builders.build_position_management_prompt(context)

    def build_profit_optimization_prompt(self, context):
        return self.prompt_builders.build_profit_optimization_prompt(context)

    def build_market_regime_prompt(self, context):
        return self.prompt_builders.build_market_regime_prompt(context)

    def build_risk_assessment_prompt(self, context):
        return self.prompt_builders.build_risk_assessment_prompt(context)

    def build_entry_timing_prompt(self, context):
        return self.prompt_builders.build_entry_timing_prompt(context)

    def build_strategy_adaptation_prompt(self, context):
        return self.prompt_builders.build_strategy_adaptation_prompt(context)

    def build_opportunity_scanner_prompt(self, context):
        return self.prompt_builders.build_opportunity_scanner_prompt(context)

    # Add response parsing methods
    def parse_emergency_response(self, response_text):
        return self.response_parsers.parse_emergency_response(response_text)

    def parse_position_management_response(self, response_text):
        return self.response_parsers.parse_position_management_response(response_text)

    def parse_profit_optimization_response(self, response_text):
        return self.response_parsers.parse_profit_optimization_response(response_text)

    def parse_market_regime_response(self, response_text):
        return self.response_parsers.parse_market_regime_response(response_text)

    def parse_risk_assessment_response(self, response_text):
        return self.response_parsers.parse_risk_assessment_response(response_text)

    def parse_entry_timing_response(self, response_text):
        return self.response_parsers.parse_entry_timing_response(response_text)

    def parse_strategy_adaptation_response(self, response_text):
        return self.response_parsers.parse_strategy_adaptation_response(response_text)

    def parse_opportunity_scanner_response(self, response_text):
        return self.response_parsers.parse_opportunity_scanner_response(response_text)

    # Add action execution methods
    def execute_emergency_actions(self, response, context):
        return self.action_executors.execute_emergency_actions(response, context)

    def execute_position_actions(self, response, context):
        return self.action_executors.execute_position_actions(response, context)

    def execute_profit_actions(self, response, context):
        return self.action_executors.execute_profit_actions(response, context)

    def execute_entry_actions(self, response, context):
        return self.action_executors.execute_entry_actions(response, context)

    def apply_strategy_adaptations(self, response, context):
        return self.action_executors.apply_strategy_adaptations(response, context)
