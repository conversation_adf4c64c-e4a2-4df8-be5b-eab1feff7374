#!/usr/bin/env python3
"""
Test script to validate LLM Orchestrator fixes
Tests response parsing and decision aggregation logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.llm_response_parsers import LLMResponseParsers
from core.llm_orchestrator import PromptType, PromptResult
from datetime import datetime

def test_response_parsing():
    """Test the enhanced response parsing capabilities"""
    print("🧪 Testing LLM Response Parsing...")
    
    parser = LLMResponseParsers()
    
    # Test cases that were failing before
    test_cases = [
        {
            'name': 'Standard Trading Decision Format',
            'response': 'DECISION: SHORT\nCONFIDENCE: 85%\nEXPLANATION: Strong bearish momentum detected',
            'expected_decision': 'SHORT',
            'expected_confidence': 85.0
        },
        {
            'name': 'Risk Assessment Format',
            'response': 'DECISION: LONG\nCONFIDENCE: 80%\nRISK_SCORE: 25\nEXPLANATION: Low risk, high reward setup',
            'expected_approved': True,
            'expected_confidence': 80.0
        },
        {
            'name': 'Entry Timing Format',
            'response': 'DECISION: SHORT\nCONFIDENCE: 75%\nACTION: ENTER_NOW\nENTRY_TYPE: LIMIT',
            'expected_action': 'ENTER_NOW',
            'expected_confidence': 75.0
        },
        {
            'name': 'Market Regime Format',
            'response': 'REGIME: RANGING_TIGHT\nCONFIDENCE: 75%\nSCALP_SUITABILITY: HIGH',
            'expected_regime': 'RANGING_TIGHT',
            'expected_confidence': 75.0
        },
        {
            'name': 'Opportunity Scanner Format',
            'response': 'DECISION: LONG\nCONFIDENCE: 80%\nBEST_OPPORTUNITY: BREAKOUT\nSETUP_TYPE: MOMENTUM',
            'expected_opportunity': 'BREAKOUT',
            'expected_confidence': 80.0
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  Testing: {test_case['name']}")
        
        # Test risk assessment parsing
        if 'expected_approved' in test_case:
            result = parser.parse_risk_assessment_response(test_case['response'])
            print(f"    Risk Assessment Result: {result}")
            
            if result.get('APPROVED') == test_case['expected_approved']:
                print(f"    ✅ APPROVED correctly parsed: {result.get('APPROVED')}")
            else:
                print(f"    ❌ APPROVED parsing failed: expected {test_case['expected_approved']}, got {result.get('APPROVED')}")
        
        # Test entry timing parsing
        elif 'expected_action' in test_case:
            result = parser.parse_entry_timing_response(test_case['response'])
            print(f"    Entry Timing Result: {result}")
            
            if result.get('ACTION') == test_case['expected_action']:
                print(f"    ✅ ACTION correctly parsed: {result.get('ACTION')}")
            else:
                print(f"    ❌ ACTION parsing failed: expected {test_case['expected_action']}, got {result.get('ACTION')}")
        
        # Test market regime parsing
        elif 'expected_regime' in test_case:
            result = parser.parse_market_regime_response(test_case['response'])
            print(f"    Market Regime Result: {result}")
            
            if result.get('REGIME') == test_case['expected_regime']:
                print(f"    ✅ REGIME correctly parsed: {result.get('REGIME')}")
            else:
                print(f"    ❌ REGIME parsing failed: expected {test_case['expected_regime']}, got {result.get('REGIME')}")
        
        # Test opportunity scanner parsing
        elif 'expected_opportunity' in test_case:
            result = parser.parse_opportunity_scanner_response(test_case['response'])
            print(f"    Opportunity Scanner Result: {result}")
            
            if result.get('BEST_OPPORTUNITY') == test_case['expected_opportunity']:
                print(f"    ✅ OPPORTUNITY correctly parsed: {result.get('BEST_OPPORTUNITY')}")
            else:
                print(f"    ❌ OPPORTUNITY parsing failed: expected {test_case['expected_opportunity']}, got {result.get('BEST_OPPORTUNITY')}")
        
        # Test confidence parsing for all cases
        if 'expected_confidence' in test_case:
            confidence = result.get('CONFIDENCE', 0)
            if abs(confidence - test_case['expected_confidence']) < 1.0:
                print(f"    ✅ CONFIDENCE correctly parsed: {confidence}%")
            else:
                print(f"    ❌ CONFIDENCE parsing failed: expected {test_case['expected_confidence']}%, got {confidence}%")

def test_decision_aggregation():
    """Test the enhanced decision aggregation logic"""
    print("\n🧪 Testing Decision Aggregation...")
    
    # Simulate cycle results that were causing issues
    mock_cycle_results = {}
    
    # Market Regime: RANGING_TIGHT with 75% confidence
    mock_cycle_results[PromptType.MARKET_REGIME] = PromptResult(
        prompt_type=PromptType.MARKET_REGIME,
        timestamp=datetime.now(),
        response={
            'REGIME': 'RANGING_TIGHT',
            'CONFIDENCE': 75,
            'SCALP_SUITABILITY': 'HIGH'
        },
        confidence=75,
        execution_time=0.5,
        success=True
    )
    
    # Risk Assessment: SHORT with 85% confidence
    mock_cycle_results[PromptType.RISK_ASSESSMENT] = PromptResult(
        prompt_type=PromptType.RISK_ASSESSMENT,
        timestamp=datetime.now(),
        response={
            'APPROVED': True,
            'RISK_SCORE': 15,  # Low risk
            'CONFIDENCE': 85
        },
        confidence=85,
        execution_time=0.3,
        success=True
    )
    
    # Entry Timing: SHORT with 75% confidence
    mock_cycle_results[PromptType.ENTRY_TIMING] = PromptResult(
        prompt_type=PromptType.ENTRY_TIMING,
        timestamp=datetime.now(),
        response={
            'ACTION': 'ENTER_NOW',
            'CONFIDENCE': 75,
            'ENTRY_TYPE': 'LIMIT'
        },
        confidence=75,
        execution_time=0.4,
        success=True
    )
    
    # Strategy Adaptation: SHORT with 60% confidence
    mock_cycle_results[PromptType.STRATEGY_ADAPTATION] = PromptResult(
        prompt_type=PromptType.STRATEGY_ADAPTATION,
        timestamp=datetime.now(),
        response={
            'RISK_ADJUSTMENT': 1.2,  # Slightly aggressive
            'CONFIDENCE': 60
        },
        confidence=60,
        execution_time=0.6,
        success=True
    )
    
    # Opportunity Scanner: LONG with 80% confidence
    mock_cycle_results[PromptType.OPPORTUNITY_SCANNER] = PromptResult(
        prompt_type=PromptType.OPPORTUNITY_SCANNER,
        timestamp=datetime.now(),
        response={
            'BEST_OPPORTUNITY': 'BREAKOUT',
            'SETUP_TYPE': 'MOMENTUM',
            'CONFIDENCE': 80
        },
        confidence=80,
        execution_time=0.7,
        success=True
    )
    
    print("  Mock Cycle Results:")
    for prompt_type, result in mock_cycle_results.items():
        print(f"    {prompt_type.value}: {result.response} (Confidence: {result.confidence}%)")
    
    # Test the aggregation logic manually
    print("\n  Testing Aggregation Logic:")
    
    decision_votes = {"LONG": 0, "SHORT": 0, "WAIT": 0, "CLOSE": 0}
    confidence_sum = 0
    confidence_count = 0
    
    # Market Regime Analysis
    regime_result = mock_cycle_results[PromptType.MARKET_REGIME]
    regime = regime_result.response.get('REGIME', 'UNKNOWN')
    scalp_suit = regime_result.response.get('SCALP_SUITABILITY', 'MEDIUM')
    
    if regime == 'RANGING_TIGHT' and scalp_suit == 'HIGH':
        decision_votes["LONG"] += 0.5
        decision_votes["SHORT"] += 0.5
        print(f"    Market Regime: +0.5 LONG, +0.5 SHORT (Range trading)")
    
    confidence_sum += regime_result.confidence
    confidence_count += 1
    
    # Risk Assessment
    risk_result = mock_cycle_results[PromptType.RISK_ASSESSMENT]
    approved = risk_result.response.get('APPROVED', False)
    risk_score = risk_result.response.get('RISK_SCORE', 100)
    
    if approved and risk_score < 70:
        decision_votes["LONG"] += 1.0
        decision_votes["SHORT"] += 1.0
        print(f"    Risk Assessment: +1.0 LONG, +1.0 SHORT (Low risk approved)")
    
    confidence_sum += risk_result.confidence
    confidence_count += 1
    
    # Entry Timing
    entry_result = mock_cycle_results[PromptType.ENTRY_TIMING]
    entry_action = entry_result.response.get('ACTION', 'WAIT')
    
    if entry_action == 'ENTER_NOW':
        decision_votes["LONG"] += 2.0
        decision_votes["SHORT"] += 2.0
        print(f"    Entry Timing: +2.0 LONG, +2.0 SHORT (Enter now)")
    
    confidence_sum += entry_result.confidence
    confidence_count += 1
    
    # Strategy Adaptation
    strategy_result = mock_cycle_results[PromptType.STRATEGY_ADAPTATION]
    risk_adj = strategy_result.response.get('RISK_ADJUSTMENT', 1.0)
    
    if risk_adj > 1.1:
        decision_votes["LONG"] += 0.8
        decision_votes["SHORT"] += 0.8
        print(f"    Strategy: +0.8 LONG, +0.8 SHORT (Aggressive {risk_adj}x)")
    
    confidence_sum += strategy_result.confidence
    confidence_count += 1
    
    # Opportunity Scanner
    opp_result = mock_cycle_results[PromptType.OPPORTUNITY_SCANNER]
    setup_type = opp_result.response.get('SETUP_TYPE', 'NONE')
    
    if setup_type == 'MOMENTUM':
        decision_votes["LONG"] += 1.2
        decision_votes["SHORT"] += 1.2
        print(f"    Opportunity: +1.2 LONG, +1.2 SHORT (Momentum setup)")
    
    confidence_sum += opp_result.confidence
    confidence_count += 1
    
    # Final decision logic
    print(f"\n  Final Vote Counts: {decision_votes}")
    
    # Apply aggressive scalping bias
    if decision_votes["LONG"] > decision_votes["SHORT"] and decision_votes["LONG"] > decision_votes["WAIT"] * 0.7:
        final_decision = "LONG"
    elif decision_votes["SHORT"] > decision_votes["LONG"] and decision_votes["SHORT"] > decision_votes["WAIT"] * 0.7:
        final_decision = "SHORT"
    else:
        final_decision = "WAIT"
    
    # Calculate confidence
    base_confidence = confidence_sum / confidence_count if confidence_count > 0 else 50
    max_votes = max(decision_votes.values())
    total_votes = sum(decision_votes.values())
    consensus_strength = max_votes / total_votes if total_votes > 0 else 0
    final_confidence = min(95, base_confidence + (consensus_strength * 20))
    
    print(f"\n  🎯 FINAL RESULT:")
    print(f"    Decision: {final_decision}")
    print(f"    Confidence: {final_confidence:.1f}%")
    print(f"    Base Confidence: {base_confidence:.1f}%")
    print(f"    Consensus Strength: {consensus_strength:.2f}")
    
    # Expected vs Actual
    print(f"\n  📊 ANALYSIS:")
    if final_decision != "WAIT":
        print(f"    ✅ SUCCESS: Decision is actionable ({final_decision}) instead of conservative WAIT")
    else:
        print(f"    ⚠️  REVIEW: Decision is still WAIT - may need further tuning")
    
    if final_confidence > 60:
        print(f"    ✅ SUCCESS: Confidence is reasonable ({final_confidence:.1f}%) instead of default 50%")
    else:
        print(f"    ⚠️  REVIEW: Confidence is low ({final_confidence:.1f}%) - may need adjustment")

if __name__ == "__main__":
    print("🚀 LLM Orchestrator Fix Validation")
    print("=" * 50)
    
    test_response_parsing()
    test_decision_aggregation()
    
    print("\n" + "=" * 50)
    print("🎯 Test Complete!")
    print("\nThe fixes should now:")
    print("1. ✅ Parse 'DECISION: SHORT, CONFIDENCE: 85%' format correctly")
    print("2. ✅ Aggregate multiple prompt results into actionable decisions")
    print("3. ✅ Calculate proper confidence from all prompt confidences")
    print("4. ✅ Apply aggressive scalping bias to reduce WAIT decisions")
    print("5. ✅ Produce decisions that reflect actual LLM analysis")
