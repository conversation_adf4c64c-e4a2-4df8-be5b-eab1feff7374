#!/usr/bin/env python3
"""
Autonomous Trading Diagnostic Script
Helps troubleshoot ScalperGPT and trading history issues
"""

import sys
import os
import json
from datetime import datetime, timed<PERSON><PERSON>

def check_trading_history_files():
    """Check for trading history and log files"""
    print("🔍 CHECKING TRADING HISTORY FILES")
    print("=" * 50)
    
    # Check for common log and data directories
    directories_to_check = [
        "logs",
        "data", 
        "trading_data",
        "sessions",
        "storage",
        "trades"
    ]
    
    files_found = []
    
    for directory in directories_to_check:
        if os.path.exists(directory):
            print(f"📁 Found directory: {directory}")
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(keyword in file.lower() for keyword in ['trade', 'history', 'verdict', 'session', 'scalper']):
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        print(f"  📄 {file_path} ({file_size} bytes, modified: {mod_time})")
                        files_found.append(file_path)
    
    # Check for SQLite databases
    for file in os.listdir('.'):
        if file.endswith('.db') or file.endswith('.sqlite'):
            file_size = os.path.getsize(file)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"🗄️ Database: {file} ({file_size} bytes, modified: {mod_time})")
            files_found.append(file)
    
    if not files_found:
        print("⚠️ No trading history files found")
        print("💡 This suggests trading history is stored in memory only")
    
    return files_found

def check_autonomous_trading_config():
    """Check autonomous trading configuration"""
    print("\n🤖 CHECKING AUTONOMOUS TRADING CONFIGURATION")
    print("=" * 50)
    
    # Check for configuration files
    config_files = [
        "config/config.yaml",
        "config/credentials.yaml", 
        "config/models_config.yaml",
        "config/trading_config.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ Found: {config_file}")
            try:
                with open(config_file, 'r') as f:
                    content = f.read()
                    if 'autonomous' in content.lower() or 'auto' in content.lower():
                        print(f"  🎯 Contains autonomous trading settings")
            except Exception as e:
                print(f"  ❌ Error reading {config_file}: {e}")
        else:
            print(f"❌ Missing: {config_file}")

def check_lmstudio_connection():
    """Check LMStudio connection and models"""
    print("\n🧠 CHECKING LMSTUDIO CONNECTION")
    print("=" * 50)
    
    try:
        import requests
        
        # Try to connect to LMStudio default port
        lmstudio_url = "http://localhost:1234/v1/models"
        
        try:
            response = requests.get(lmstudio_url, timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ LMStudio connected - {len(models.get('data', []))} models available")
                for model in models.get('data', []):
                    print(f"  🤖 Model: {model.get('id', 'Unknown')}")
            else:
                print(f"⚠️ LMStudio responded with status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to LMStudio (http://localhost:1234)")
            print("💡 Make sure LMStudio is running and serving on port 1234")
        except Exception as e:
            print(f"❌ Error checking LMStudio: {e}")
            
    except ImportError:
        print("❌ requests library not available for LMStudio check")

def check_exchange_connection():
    """Check exchange connection and credentials"""
    print("\n💱 CHECKING EXCHANGE CONNECTION")
    print("=" * 50)
    
    try:
        # Check credentials file
        if os.path.exists("config/credentials.yaml"):
            print("✅ Credentials file found")
            
            try:
                import yaml
                with open("config/credentials.yaml", 'r') as f:
                    creds = yaml.safe_load(f)
                
                if 'apiKey' in creds or 'api_key' in creds:
                    print("✅ API key configured")
                else:
                    print("❌ No API key found in credentials")
                    
                if 'secretKey' in creds or 'secret_key' in creds or 'secret' in creds:
                    print("✅ Secret key configured")
                else:
                    print("❌ No secret key found in credentials")
                    
            except Exception as e:
                print(f"❌ Error reading credentials: {e}")
        else:
            print("❌ No credentials file found")
            print("💡 Create config/credentials.yaml with your HTX API credentials")
            
    except ImportError:
        print("❌ yaml library not available for credentials check")

def analyze_recent_logs():
    """Analyze recent log files for trading activity"""
    print("\n📋 ANALYZING RECENT LOGS")
    print("=" * 50)
    
    # Look for recent log files
    log_patterns = ['*.log', '*.txt']
    recent_logs = []
    
    # Check current directory and logs subdirectory
    for directory in ['.', 'logs']:
        if os.path.exists(directory):
            for file in os.listdir(directory):
                if file.endswith('.log') or file.endswith('.txt'):
                    file_path = os.path.join(directory, file)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if mod_time > datetime.now() - timedelta(days=1):  # Last 24 hours
                        recent_logs.append((file_path, mod_time))
    
    if recent_logs:
        print(f"📄 Found {len(recent_logs)} recent log files:")
        
        for log_file, mod_time in sorted(recent_logs, key=lambda x: x[1], reverse=True):
            print(f"  📝 {log_file} (modified: {mod_time})")
            
            # Analyze log content for trading keywords
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    # Count trading-related entries
                    scalper_count = content.lower().count('scalper')
                    auto_trader_count = content.lower().count('auto trader')
                    trade_executed_count = content.lower().count('trade executed')
                    error_count = content.lower().count('error')
                    
                    if scalper_count > 0:
                        print(f"    🤖 ScalperGPT mentions: {scalper_count}")
                    if auto_trader_count > 0:
                        print(f"    🔄 Auto Trader mentions: {auto_trader_count}")
                    if trade_executed_count > 0:
                        print(f"    ✅ Trade executions: {trade_executed_count}")
                    if error_count > 0:
                        print(f"    ❌ Errors: {error_count}")
                        
            except Exception as e:
                print(f"    ❌ Error reading {log_file}: {e}")
    else:
        print("⚠️ No recent log files found")
        print("💡 This might indicate logging is not configured or files are being cleared")

def check_system_requirements():
    """Check system requirements and dependencies"""
    print("\n🔧 CHECKING SYSTEM REQUIREMENTS")
    print("=" * 50)
    
    # Check Python version
    print(f"🐍 Python version: {sys.version}")
    
    # Check required packages
    required_packages = [
        'PyQt5', 'ccxt', 'numpy', 'pandas', 'requests', 'yaml', 'sqlite3'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} available")
        except ImportError:
            print(f"❌ {package} missing")
            
    # Check available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"💾 Available memory: {memory.available / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB")
        
        if memory.available < 1024**3:  # Less than 1GB
            print("⚠️ Low memory - may affect performance")
    except ImportError:
        print("⚠️ psutil not available - cannot check memory")

def generate_diagnostic_report():
    """Generate comprehensive diagnostic report"""
    print("🚀 EPINNOX AUTONOMOUS TRADING DIAGNOSTICS")
    print("=" * 60)
    print(f"📅 Report generated: {datetime.now()}")
    print("=" * 60)
    
    # Run all diagnostic checks
    check_system_requirements()
    check_autonomous_trading_config()
    check_lmstudio_connection()
    check_exchange_connection()
    check_trading_history_files()
    analyze_recent_logs()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    print("\n📋 NEXT STEPS:")
    print("1. ✅ Ensure LMStudio is running with a loaded model")
    print("2. ✅ Verify HTX API credentials are configured")
    print("3. ✅ Launch Epinnox: python launch_epinnox.py")
    print("4. ✅ Follow activation sequence: Analyze Symbol → LLM Orchestrator → Auto Trader")
    print("5. ✅ Monitor logs for trading activity")
    
    print("\n🔍 TROUBLESHOOTING:")
    print("• If trading history is empty: Check if Auto Trader is actually enabled")
    print("• If no trades executing: Check daily trade limits and emergency stop status")
    print("• If LLM errors: Restart LMStudio and reload model")
    print("• If position detection fails: Restart Epinnox application")
    
    print("\n📞 SUPPORT:")
    print("• Review AUTONOMOUS_TRADING_ACTIVATION_GUIDE.md for detailed steps")
    print("• Check console output for real-time error messages")
    print("• Monitor ScalperGPT Trading History panel for trade records")

if __name__ == "__main__":
    generate_diagnostic_report()
